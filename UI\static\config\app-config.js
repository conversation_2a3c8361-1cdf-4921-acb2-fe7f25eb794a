/**
 * 应用配置文件
 * 此文件可以在项目发布后直接修改，无需重新编译
 *
 * 使用方法：
 * 1. 开发环境：修改此文件后刷新页面即可生效
 * 2. 生产环境：直接修改服务器上的此文件，用户刷新页面后生效
 */

window.APP_CONFIG = {
  // ===== 基础配置 =====
  // 项目前端访问地址
  // 开发环境示例：'http://localhost:5173'
  // 生产环境示例：'https://your-domain.com' 或 'http://your-server-ip:port'
  UIProjectUrl: 'http://localhost:5173',

  // 应用信息
  appName: '视频学习测验系统',
  version: '1.0.0',

  // 是否启用调试模式
  debugMode: true,

  // ===== API配置 =====
  // API基础地址
  // 开发环境示例：'https://localhost:7048/api'
  // 生产环境示例：'https://your-api-domain.com/api' 或 'http://your-server-ip:port/api'
  apiBaseUrl: 'https://localhost:7048/api',

  // API请求配置
  apiTimeout: 30000,        // API超时时间（毫秒）
  apiRetryCount: 3,         // 重试次数
  apiRetryDelay: 1000,      // 重试延迟（毫秒）

  // 默认请求头
  apiDefaultHeaders: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },

  // ===== 上传配置 =====
  upload: {
    maxFileSize: 2 * 1024 * 1024 * 1024, // 最大文件大小 2GB
    allowedVideoFormats: ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'],
    allowedImageFormats: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
  },

  // ===== 压缩配置 =====
  compression: {
    defaultQuality: 7,        // 默认压缩质量
    qualityRange: [1, 10],    // 质量范围
    defaultEnabled: true      // 默认启用压缩
  },

  // ===== 功能开关 =====
  features: {
    enableCache: true,        // 是否启用缓存
    enableOffline: false,     // 是否启用离线模式
    enableAnalytics: false    // 是否启用数据分析
  },

  // ===== UI配置 =====
  ui: {
    // 主题色彩
    colors: {
      primary: '#186BFF',
      success: '#52C41A',
      warning: '#FAAD14',
      error: '#F5222D',
      info: '#186BFF'
    },

    // 页面配置
    pageSize: 20,             // 默认分页大小
    maxUploadSize: 2 * 1024 * 1024 * 1024, // 最大上传文件大小 2GB

    // 动画配置
    animationDuration: 300    // 默认动画时长
  },

  // ===== 存储配置 =====
  storage: {
    // 存储过期时间（毫秒）
    expireTime: {
      token: 7 * 24 * 60 * 60 * 1000,    // 7天
      userInfo: 24 * 60 * 60 * 1000,     // 1天
      cache: 30 * 60 * 1000              // 30分钟
    }
  }
};

// 提供获取配置的方法
window.getAppConfig = function (key) {
  return window.APP_CONFIG[key];
};

// 提供设置配置的方法（用于动态修改）
window.setAppConfig = function (key, value) {
  window.APP_CONFIG[key] = value;
};
