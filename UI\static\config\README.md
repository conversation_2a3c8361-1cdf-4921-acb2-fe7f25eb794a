# 应用配置说明

## 概述

此目录包含应用的外部配置文件，这些文件可以在项目发布后直接修改，无需重新编译和发布。

## 配置文件

### app-config.js

主要的应用配置文件，包含以下配置项：

#### 基础配置

- `UIProjectUrl`: 项目前端访问地址
  - 默认值: `http://localhost:5173`
  - 示例: 开发环境 `http://localhost:5173`，生产环境 `https://your-domain.com`
- `appName`: 应用名称
- `version`: 应用版本号
- `debugMode`: 是否启用调试模式

#### API 配置

- `apiBaseUrl`: API 基础地址
  - 默认值: `https://localhost:7048/api`
  - 示例: 生产环境 `https://your-api-domain.com/api`
- `apiTimeout`: API 请求超时时间（毫秒）
- `apiRetryCount`: API 请求重试次数
- `apiRetryDelay`: API 请求重试延迟（毫秒）
- `apiDefaultHeaders`: 默认请求头

#### 上传配置

- `upload.maxFileSize`: 最大文件大小（字节）
- `upload.allowedVideoFormats`: 允许的视频格式
- `upload.allowedImageFormats`: 允许的图片格式

#### 压缩配置

- `compression.defaultQuality`: 默认压缩质量（1-10）
- `compression.qualityRange`: 质量范围
- `compression.defaultEnabled`: 是否默认启用压缩

#### 功能开关

- `features.enableCache`: 是否启用缓存
- `features.enableOffline`: 是否启用离线模式
- `features.enableAnalytics`: 是否启用数据分析

#### UI 配置

- `ui.colors`: 主题色彩配置
- `ui.pageSize`: 默认分页大小
- `ui.maxUploadSize`: 最大上传文件大小
- `ui.animationDuration`: 默认动画时长

#### 存储配置

- `storage.expireTime.token`: Token 过期时间
- `storage.expireTime.userInfo`: 用户信息过期时间
- `storage.expireTime.cache`: 缓存过期时间

## 如何修改配置

### 开发环境

1. 直接修改 `UI/static/config/app-config.js` 文件
2. 保存后刷新浏览器页面即可生效

### 生产环境

1. 找到服务器上的配置文件路径：`/path/to/your/project/static/config/app-config.js`
2. 使用文本编辑器修改配置文件
3. 保存文件
4. 用户刷新页面后新配置即可生效

## 注意事项

1. **语法正确性**: 修改配置时请确保 JavaScript 语法正确，否则可能导致应用无法正常运行
2. **备份**: 修改前建议备份原配置文件
3. **测试**: 修改后请测试相关功能是否正常
4. **缓存**: 如果修改后没有生效，请清除浏览器缓存或强制刷新页面

## 配置示例

```javascript
window.APP_CONFIG = {
  // 生产环境配置示例
  UIProjectUrl: 'https://your-domain.com',
  appName: '视频学习测验系统',
  version: '1.0.0',
  debugMode: false,

  // API配置
  apiBaseUrl: 'https://your-api-domain.com/api',
  apiTimeout: 30000,

  // 上传配置
  upload: {
    maxFileSize: 2 * 1024 * 1024 * 1024, // 2GB
    allowedVideoFormats: [
      '.mp4',
      '.avi',
      '.mov',
      '.mkv',
      '.wmv',
      '.flv',
      '.webm',
    ],
    allowedImageFormats: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'],
  },

  // 其他配置项...
}
```

## 故障排除

如果修改配置后出现问题：

1. 检查 JavaScript 语法是否正确
2. 查看浏览器控制台是否有错误信息
3. 恢复备份的配置文件
4. 联系技术支持
