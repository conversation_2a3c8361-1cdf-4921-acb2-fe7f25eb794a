const e={1:"active",0:"disabled",2:"dismissed"},t={1:"admin",2:"manager",3:"employee"},a={1:"超管",2:"管理",3:"员工"},s={employee:1,manager:2,admin:3};function i(s){return s?{id:s.id,username:s.userName,realName:s.realName,avatar:s.avatar,phone:s.mobile||"",email:s.email||"",role:t[s.userType]||"employee",level:s.userType,userType:s.userType,userTypeName:s.userTypeName||a[s.userType]||"",parentUserId:s.parentUserId,parentUserName:s.parentUserName||"",managerId:s.parentUserId,managerName:s.parentUserName||"",roleName:s.roleName,roleCode:s.roleCode,department:"默认部门",position:s.userTypeName||a[s.userType]||"员工",registerTime:s.createTime||(new Date).toISOString(),lastLoginTime:s.lastLoginTime||"",lastLoginIp:s.lastLoginIp||"",status:e[s.status]||"active",statusCode:s.status,disabled:1!==s.status,dismissed:0===s.status,type:t[s.userType]||"employee",employeeCount:s.directSubordinateCount||0,userCount:s.totalSubordinateUserCount||0,directSubordinateCount:s.directSubordinateCount||0,totalSubordinateUserCount:s.totalSubordinateUserCount||0,statistics:s.statistics||{},totalViews:r(s.statistics,"views"),totalQuizzes:r(s.statistics,"quizzes"),totalRewards:r(s.statistics,"rewards"),remark:s.remark||"",createTime:s.createTime,updateTime:s.updateTime}:null}function r(e,t){if(!e||!e[t])return{today:0,yesterday:0,thisWeek:0,thisMonth:0};const a=e[t];return{today:a.today||0,yesterday:a.yesterday||0,thisWeek:a.thisWeek||0,thisMonth:a.thisMonth||0}}function o(a){return a?a.userName&&void 0!==a.userType?i(a):{id:a.employeeId||a.id,username:a.name||a.username,realName:a.realName||a.name,avatar:a.avatar,phone:a.phone||"",email:a.email||"",role:t[a.level]||"employee",level:a.level,managerId:a.managerId,managerName:a.managerName||"",department:a.department||"默认部门",position:a.position||"员工",registerTime:a.createTime||(new Date).toISOString(),lastLoginTime:a.lastLoginTime||"",status:e[a.status]||"active",statusCode:a.status,disabled:1!==a.status,dismissed:2===a.status,type:t[a.level]||"employee",employeeCount:a.employeeCount||0,userCount:a.userCount||0,totalViews:{today:a.todayViews||0,yesterday:a.yesterdayViews||0,thisWeek:a.thisWeekViews||0,thisMonth:a.thisMonthViews||0},totalQuizzes:{today:a.todayQuizzes||0,yesterday:a.yesterdayQuizzes||0,thisWeek:a.thisWeekQuizzes||0,thisMonth:a.thisMonthQuizzes||0},totalRewards:{today:a.todayRewards||0,yesterday:a.yesterdayRewards||0,thisWeek:a.thisWeekRewards||0,thisMonth:a.thisMonthRewards||0},remark:a.remark||"",createTime:a.createTime,updateTime:a.updateTime}:null}function n(t){return t?{id:t.id||t.userId,username:t.nickname||t.username||t.name||"未设置",realName:t.realName||t.nickname||t.name||"未设置",avatar:t.avatar,phone:t.phone||t.mobile||"",email:t.email||"",role:"user",type:"user",employeeId:t.employeeId,employeeName:t.employeeName||"",registerTime:t.createTime||t.registerTime||(new Date).toISOString(),lastLoginTime:t.lastLogin||t.lastLoginTime||"",status:void 0!==t.status&&e[t.status]||"active",statusCode:t.status||1,disabled:void 0!==t.status&&1!==t.status,openId:t.openId||"",unionId:t.unionId||"",nickname:t.nickname||"",watchedVideos:t.watchedVideos||0,totalViews:t.totalViews||{today:t.todayViews||0,yesterday:t.yesterdayViews||0,thisWeek:t.thisWeekViews||0,thisMonth:t.thisMonthViews||0},totalQuizzes:t.totalQuizzes||{today:t.todayQuizzes||0,yesterday:t.yesterdayQuizzes||0,thisWeek:t.thisWeekQuizzes||0,thisMonth:t.thisMonthQuizzes||0},totalRewards:t.totalRewards||{today:t.todayRewards||0,yesterday:t.yesterdayRewards||0,thisWeek:t.thisWeekRewards||0,thisMonth:t.thisMonthRewards||0}}:null}function m(e,t="employee"){return{employeeId:e.userName||e.username,name:e.userName||e.username,phone:e.phone||"",email:e.email||"",department:e.department||"默认部门",position:e.position||("manager"===t?"管理":"员工"),level:s[t]||1,managerId:e.managerId||null,status:1,remark:e.remark||"通过管理后台创建的"+("manager"===t?"管理":"员工")}}export{o as a,n as b,m as c,i as f};
