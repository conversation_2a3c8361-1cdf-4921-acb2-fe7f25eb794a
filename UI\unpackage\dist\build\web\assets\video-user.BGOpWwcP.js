import{m as e}from"./index-EyT8-w9G.js";function n(n){return e.post("/User/register",n)}function t(n){return e.post("/User/simple-login",n)}function a(n){return e.post("/User/login",n)}function r(n){return e.get(`/User/${n}`)}function i(n){return e.get("/User",n)}function o(e,n={}){return i({EmployeeId:e,PageIndex:n.PageIndex||1,PageSize:n.PageSize||20})}function s(e){return e?{...e,lastLogin:e.lastLogin?new Date(e.lastLogin).toLocaleString():"从未登录",createTime:e.createTime?new Date(e.createTime).toLocaleString():"",avatar:e.avatar,nickname:e.nickname||"未设置昵称",employeeName:e.employeeName||"未绑定员工"}:{}}export{t as a,o as b,s as f,r as g,i as q,n as s,a as w};
