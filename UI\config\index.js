/**
 * 应用配置
 * 现在从外部配置文件 (window.APP_CONFIG) 读取配置
 */

/**
 * 获取外部配置项
 * @param {string} key 配置项键名
 * @param {any} defaultValue 默认值
 * @returns {any} 配置项值
 */
function getExternalConfig(key, defaultValue = null) {
  if (window.APP_CONFIG && window.APP_CONFIG[key] !== undefined) {
    return window.APP_CONFIG[key];
  }
  return defaultValue;
}

// 应用配置 - 从外部配置文件读取
const config = {
  // API基础地址
  get apiBaseUrl() {
    return getExternalConfig('apiBaseUrl',
      process.env.NODE_ENV === 'development'
        ? 'https://localhost:7048'
        : 'https://your-production-api.com'
    );
  },

  // 上传配置
  get upload() {
    return getExternalConfig('upload', {
      maxFileSize: 2 * 1024 * 1024 * 1024, // 2GB
      allowedVideoFormats: ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'],
      allowedImageFormats: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
    });
  },

  // 压缩配置
  get compression() {
    return getExternalConfig('compression', {
      defaultQuality: 7,
      qualityRange: [1, 10],
      defaultEnabled: true
    });
  }
};

export default config;
