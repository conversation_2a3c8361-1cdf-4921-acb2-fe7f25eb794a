import{a as e,N as s,R as t,T as r,v as a,M as n,_ as i,q as o,u as l,U as c,V as u,W as d,p as g,r as m,b as h,c as p,w as f,X as P,i as y,Y as U,e as _,Z as k,$ as b,o as T,h as v,j as S,t as I,k as L,A as q,B as x,F as w,l as C}from"./index-EyT8-w9G.js";import{p as D}from"./permission-mixin.OCUMGXbm.js";function M(s={}){return function(s={},t=[],r=[]){if(!e.isLoggedIn())return e.redirectToLogin(),!1;if(!e.isSessionValid())return e.logout(),e.redirectToLogin(),!1;if(r.length>0){const s=e.getUserType();if(!r.includes(s))return a({title:"您没有权限访问此页面",icon:"none"}),setTimeout((()=>{n()}),1500),!1}if(t.length>0&&!t.every((s=>e.hasPermission(s))))return a({title:"您没有权限访问此功能",icon:"none"}),setTimeout((()=>{n()}),1500),!1;return e.refreshSession(),!0}(s,[],[])}function $(){if(!e.isLoggedIn())return{name:"未登录",username:"",userType:"",userTypeText:"游客",avatar:"",email:"",phone:"",department:"",position:""};const s=e.getLoginInfo();return{name:s.nickName||s.username||"未知用户",username:s.username||"",userType:s.userType||"",userTypeText:{employee:"员工",manager:"管理",admin:"超管"}[s.userType]||"未知",avatar:s.avatar||"",email:s.email||"",phone:s.phone||"",department:s.department||"",position:s.position||""}}async function N(s=!1){return await e.getCompleteUserInfo(s)}const R=i({mixins:[D],data:()=>({currentUser:{},loading:!1,mainPages:[{name:"数据统计",path:"/pages/index/index",icon:"icon-dashboard",requiredPermission:"view_dashboard"}],personalPages:[{name:"修改密码",path:"/pages/user/change-password",icon:"icon-lock",requiredPermission:null}],userPages:[{name:"管理信息",path:"/pages/admin/users/manager-list",icon:"icon-users",requiredRoles:["admin","super_admin"]},{name:"员工信息",path:"/pages/admin/users/employee-list",icon:"icon-staff",requiredRoles:["manager","agent","admin","super_admin"]},{name:"用户信息",path:"/pages/admin/users/user-list",icon:"icon-user",requiredPermission:"manage_users"},{name:"用户审核",path:"/pages/admin/users/audit/user-audit",icon:"icon-audit",requiredPermission:"user_audit"}]}),computed:{userTypeText(){return{employee:"员工",manager:"经理",admin:"管理员",agent:"管理"}[this.currentUser.userType]||"用户"},formatLastLoginTime(){if(!this.currentUser.lastLoginTime)return"从未登录";try{const e=new Date(this.currentUser.lastLoginTime),s=new Date;if(s-e<864e5&&e.getDate()===s.getDate())return`今天 ${e.getHours().toString().padStart(2,"0")}:${e.getMinutes().toString().padStart(2,"0")}`;const t=new Date(s);return t.setDate(t.getDate()-1),e.getDate()===t.getDate()&&e.getMonth()===t.getMonth()?`昨天 ${e.getHours().toString().padStart(2,"0")}:${e.getMinutes().toString().padStart(2,"0")}`:`${e.getFullYear()}-${(e.getMonth()+1).toString().padStart(2,"0")}-${e.getDate().toString().padStart(2,"0")} ${e.getHours().toString().padStart(2,"0")}:${e.getMinutes().toString().padStart(2,"0")}`}catch(e){return this.currentUser.lastLoginTime}},visibleMainPages(){return this.mainPages.filter((e=>this.hasPagePermission(e)))},visiblePersonalPages(){return this.personalPages.filter((e=>this.hasPagePermission(e)))},visibleUserPages(){return this.userPages.filter((e=>this.hasPagePermission(e)))}},onLoad(e){M(e)&&this.loadUserInfo()},methods:{hasPagePermission(e){return!e.requiredPermission&&!e.requiredRoles||(e.requiredPermission?this.canUseFeature(e.requiredPermission):!!e.requiredRoles&&this.hasRole(e.requiredRoles))},async loadUserInfo(){try{this.currentUser=$();const e=await N(!1);e&&(this.currentUser=e)}catch(e){console.error("加载用户信息失败:",e),this.currentUser=$()}},async refreshUserInfo(){if(!this.loading){this.loading=!0,o({title:"刷新用户信息..."});try{const e=await N(!0);e&&(this.currentUser=e,a({title:"用户信息已更新",icon:"success"}))}catch(e){console.error("刷新用户信息失败:",e),a({title:"刷新失败，请重试",icon:"none"})}finally{this.loading=!1,l()}}},copyUserId(){this.currentUser.userId?c({data:this.currentUser.userId.toString(),success:()=>{a({title:"用户ID已复制",icon:"success"})},fail:()=>{a({title:"复制失败",icon:"none"})}}):a({title:"用户ID不存在",icon:"none"})},navigateTo(e){u({url:e,fail:()=>{d({url:e,fail:()=>{g({url:e})}})}})},showLogoutConfirm(){!function(a="确定要退出登录吗？"){s({title:"确认退出",content:a,success:async s=>{if(s.confirm)try{await e.logout()?(t("已退出登录"),setTimeout((()=>{e.redirectToLogin()}),1500)):r("退出失败，请重试")}catch(a){r("退出失败，请重试")}}})}()},getIconName:e=>({"icon-dashboard":"grid","icon-video":"play-circle","icon-users":"account","icon-staff":"account-fill","icon-user":"account","icon-info":"info-circle","icon-audit":"checkmark-circle","icon-lock":"lock"}[e]||"more-circle")}},[["render",function(e,s,t,r,a,n){const i=m(h("u-avatar"),P),o=y,l=C,c=m(h("u-tag"),U),u=m(h("u-icon"),_),d=m(h("u-cell"),k),g=m(h("u-cell-group"),b);return T(),p(o,{class:"wx-center-container"},{default:f((()=>[v(o,{class:"user-header"},{default:f((()=>[v(g,{border:!1},{default:f((()=>[v(d,{border:!1,onClick:n.refreshUserInfo},{icon:f((()=>[v(o,{class:"avatar-container"},{default:f((()=>[v(i,{src:a.currentUser.avatar||"/assets/images/avatar-placeholder.png",size:"60",shape:"square"},null,8,["src"])])),_:1})])),title:f((()=>[v(o,{class:"user-info"},{default:f((()=>[v(o,{class:"user-name"},{default:f((()=>[S(I(a.currentUser.realName||a.currentUser.name||"用户"),1)])),_:1}),v(o,{class:"user-meta"},{default:f((()=>[v(l,{class:"user-phone"},{default:f((()=>[S(I(a.currentUser.phone||a.currentUser.username||""),1)])),_:1}),v(c,{text:n.userTypeText,type:"primary",size:"mini",plain:!0},null,8,["text"])])),_:1}),a.currentUser.email||a.currentUser.department?(T(),p(o,{key:0,class:"user-extra-info"},{default:f((()=>[a.currentUser.email?(T(),p(l,{key:0,class:"user-email"},{default:f((()=>[S(I(a.currentUser.email),1)])),_:1})):L("",!0),a.currentUser.department?(T(),p(l,{key:1,class:"user-department"},{default:f((()=>[S(I(a.currentUser.department),1)])),_:1})):L("",!0)])),_:1})):L("",!0)])),_:1})])),"right-icon":f((()=>[v(u,{name:"arrow-right",color:"#c0c4cc",size:"16"})])),_:1},8,["onClick"])])),_:1})])),_:1}),n.visibleMainPages.length>0?(T(),p(o,{key:0,class:"menu-section"},{default:f((()=>[v(g,{border:!1},{default:f((()=>[(T(!0),q(w,null,x(n.visibleMainPages,((e,s)=>(T(),p(d,{key:s,title:e.name,isLink:!0,border:s!==n.visibleMainPages.length-1,onClick:s=>n.navigateTo(e.path)},{icon:f((()=>[v(u,{name:n.getIconName(e.icon),size:"20",color:"#666"},null,8,["name"])])),_:2},1032,["title","border","onClick"])))),128))])),_:1})])),_:1})):L("",!0),n.visiblePersonalPages.length>0?(T(),p(o,{key:1,class:"menu-section"},{default:f((()=>[v(g,{border:!1},{default:f((()=>[(T(!0),q(w,null,x(n.visiblePersonalPages,((e,s)=>(T(),p(d,{key:s,title:e.name,isLink:!0,border:s!==n.visiblePersonalPages.length-1,onClick:s=>n.navigateTo(e.path)},{icon:f((()=>[v(u,{name:n.getIconName(e.icon),size:"20",color:"#666"},null,8,["name"])])),_:2},1032,["title","border","onClick"])))),128))])),_:1})])),_:1})):L("",!0),n.visibleUserPages.length>0?(T(),p(o,{key:2,class:"menu-section"},{default:f((()=>[v(g,{border:!1},{default:f((()=>[(T(!0),q(w,null,x(n.visibleUserPages,((e,s)=>(T(),p(d,{key:s,title:e.name,isLink:!0,border:s!==n.visibleUserPages.length-1,onClick:s=>n.navigateTo(e.path)},{icon:f((()=>[v(u,{name:n.getIconName(e.icon),size:"20",color:"#666"},null,8,["name"])])),_:2},1032,["title","border","onClick"])))),128))])),_:1})])),_:1})):L("",!0),v(o,{class:"logout-section"},{default:f((()=>[v(g,{border:!1},{default:f((()=>[v(d,{title:"退出登录",isLink:!0,border:!1,onClick:n.showLogoutConfirm},{title:f((()=>[v(l,{class:"logout-text"},{default:f((()=>[S("退出登录")])),_:1})])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-1e064c51"]]);export{R as default};
