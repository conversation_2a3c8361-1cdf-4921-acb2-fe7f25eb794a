import{L as e,q as t,u as s,R as r,T as a,N as i,V as o,M as u,p as n}from"./index-EyT8-w9G.js";import{b as c,c as d,f as l}from"./formatter.ClY2FBgV.js";const m={methods:{formatDuration:e=>c(e),formatDate:e=>d(e),formatSimpleDate:e=>l(e),buildCompleteFileUrl(t){if(!t)return"";if(t.startsWith("http://")||t.startsWith("https://"))return t;const s=e();if(!s||""===s.trim())return t;return`${s.replace("/api","")}/wwwroot${t.startsWith("/")?t:`/${t}`}`},mapVideoStatus:e=>({0:"scheduled",1:"active",2:"expired"}[e]||"scheduled"),getStatusText(e){if(void 0!==e.compressionStatus){return{0:"未压缩",1:"压缩中",2:"已压缩",3:"压缩失败"}[e.compressionStatus]||"未知压缩状态"}return{expired:"已过期",scheduled:"待发布",active:"已上线"}[e.status]||"未知"},getStatusType(e){if(void 0!==e.compressionStatus){return{0:"info",1:"warning",2:"success",3:"error"}[e.compressionStatus]||"info"}return{expired:"error",scheduled:"warning",active:"success"}[e.status]||"default"},getStatusLabel:e=>({all:"全部",active:"已上线",scheduled:"待发布",expired:"已过期"}[e]||"全部"),showLoading(e="加载中..."){t({title:e,mask:!0})},hideLoading(){s()},showSuccess(e){r(e)},showError(e){a(e)},showConfirm:(e,t)=>new Promise((s=>{i({title:e,content:t,success:e=>{s(e.confirm)},fail:()=>{s(!1)}})})),safeNavigateTo(e,t={}){o({url:e,...t,fail:e=>{console.error("页面跳转失败:",e),this.showError("页面跳转失败")}})},safeNavigateBack(){u({fail:e=>{console.error("页面返回失败:",e),n({url:"/pages/admin/media/index"})}})}}};export{m};
