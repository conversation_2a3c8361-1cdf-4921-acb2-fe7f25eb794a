<template>
    <view class="container">
        <!-- 顶部标题栏 -->
        <PageHeader title="用户转移" />

        <!-- 源员工信息 -->
        <view class="section source-employee" v-if="sourceEmployee">
            <view class="section-header">
                <text class="section-title">源员工信息</text>
            </view>
            <view class="employee-info">
                <image class="avatar" :src="sourceEmployee.avatar || '/assets/images/avatar-placeholder.png'"></image>
                <view class="info-content">
                    <text class="name">{{ sourceEmployee.username }}</text>
                    <text class="status" :class="{ 'dismissed': sourceEmployee.dismissed }">
                        {{ sourceEmployee.dismissed ? '已离职' : '在职中' }}
                    </text>
                </view>
            </view>
        </view>

        <!-- 目标员工选择 -->
        <view class="section target-employee">
            <view class="section-header">
                <text class="section-title">目标员工</text>
            </view>

            <view class="employee-selector">
                <view class="employee-item" v-for="employee in availableEmployees" :key="employee.id"
                    :class="{ 'selected': selectedEmployeeId === employee.id }" @tap="selectTargetEmployee(employee)">
                    <image class="avatar" :src="employee.avatar || '/assets/images/avatar-placeholder.png'"></image>
                    <text class="name">{{ employee.username }}</text>
                    <view class="select-icon" v-if="selectedEmployeeId === employee.id">
                        <text class="iconfont icon-check"></text>
                    </view>
                </view>
            </view>
        </view>

        <!-- 用户列表 -->
        <view class="section users-section">
            <view class="section-header">
                <text class="section-title">待转移用户 ({{ filteredUsers.length }})</text>
                <view class="header-actions">
                    <view class="select-all" @tap="toggleSelectAll">
                        <text>{{ allSelected ? '取消全选' : '全选' }}</text>
                    </view>
                </view>
            </view>

            <view class="users-list">
                <view class="user-item" v-for="user in filteredUsers" :key="user.id"
                    :class="{ 'selected': selectedUserIds.includes(user.id) }" @tap="toggleSelectUser(user)">
                    <view class="select-box">
                        <view class="checkbox" :class="{ 'checked': selectedUserIds.includes(user.id) }"></view>
                    </view>
                    <image class="avatar" :src="user.avatar || '/assets/images/avatar-placeholder.png'"></image>
                    <view class="user-info">
                        <text class="name">{{ user.username }}</text>
                        <text class="member-since">注册时间: {{ formatDate(user.registerTime) }}</text>
                    </view>
                </view>

                <view class="empty-state" v-if="filteredUsers.length === 0">
                    <text>没有可转移的用户</text>
                </view>
            </view>
        </view>

        <!-- 底部操作栏 -->
        <view class="footer-actions">
            <view class="selected-count">已选择: {{ selectedUserIds.length }} 个用户</view>
            <button class="transfer-btn" :disabled="selectedUserIds.length === 0 || !selectedEmployeeId"
                @tap="confirmTransfer">
                确认转移
            </button>
        </view>

        <!-- 转移确认弹窗 -->
        <view class="modal" v-if="showTransferConfirmModal">
            <view class="modal-content">
                <view class="modal-header">
                    <text class="modal-title">确认转移</text>
                    <text class="close-btn" @tap="closeTransferConfirmModal">×</text>
                </view>
                <view class="modal-body">
                    <text class="modal-text">
                        确定要将选中的 {{ selectedUserIds.length }} 个用户从 {{ sourceEmployee ? sourceEmployee.username : '' }}
                        转移至
                        {{ targetEmployee ? targetEmployee.username : '' }} 吗？
                    </text>
                    <view class="modal-btns">
                        <button class="modal-btn cancel" @tap="closeTransferConfirmModal">取消</button>
                        <button class="modal-btn confirm" @tap="executeTransfer">确认</button>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import PageHeader from '../../../components/PageHeader.vue';

export default {
    components: {
        PageHeader
    },
    data () {
        return {
            sourceEmployeeId: null,
            sourceEmployee: null,
            users: [],
            selectedEmployeeId: null,
            targetEmployee: null,
            selectedUserIds: [],
            showTransferConfirmModal: false
        }
    },
    computed: {
        // 获取可选的目标员工列表（排除源员工和已离职员工）
        availableEmployees () {
            // TODO: 从API获取员工列表数据
            // 暂时返回空数组，等待API实现
            return [];
        },

        // 获取源员工的用户
        filteredUsers () {
            return this.users.filter(user =>
                user.employeeId === this.sourceEmployeeId
            );
        },

        // 是否全选
        allSelected () {
            return this.filteredUsers.length > 0 && this.selectedUserIds.length === this.filteredUsers.length;
        }
    },
    onLoad (options) {
        // 获取源员工ID
        if (options.employeeId) {
            this.sourceEmployeeId = parseInt(options.employeeId);

            // TODO: 从API获取源员工信息和用户数据
            this.sourceEmployee = null;
            this.users = [];
        }
    },
    methods: {
        // 选择目标员工
        selectTargetEmployee (employee) {
            this.selectedEmployeeId = employee.id;
            this.targetEmployee = employee;
        },

        // 切换选择用户
        toggleSelectUser (user) {
            const index = this.selectedUserIds.indexOf(user.id);
            if (index === -1) {
                // 添加到选择列表
                this.selectedUserIds.push(user.id);
            } else {
                // 从选择列表移除
                this.selectedUserIds.splice(index, 1);
            }
        },

        // 切换全选
        toggleSelectAll () {
            if (this.allSelected) {
                // 取消全选
                this.selectedUserIds = [];
            } else {
                // 全选
                this.selectedUserIds = this.filteredUsers.map(user => user.id);
            }
        },

        // 显示转移确认弹窗
        confirmTransfer () {
            if (this.selectedUserIds.length > 0 && this.selectedEmployeeId) {
                this.showTransferConfirmModal = true;
            }
        },

        // 关闭转移确认弹窗
        closeTransferConfirmModal () {
            this.showTransferConfirmModal = false;
        },

        // 执行转移
        executeTransfer () {
            // 实际应用中应该调用API
            // 这里只是模拟本地数据更新
            this.users.forEach(user => {
                if (this.selectedUserIds.includes(user.id)) {
                    user.employeeId = this.selectedEmployeeId;
                }
            });

            uni.showToast({
                title: '用户转移成功',
                icon: 'success'
            });

            this.closeTransferConfirmModal();

            // 延迟返回上一页
            setTimeout(() => {
                uni.navigateBack();
            }, 1500);
        },

        // 格式化日期
        formatDate (timestamp) {
            if (!timestamp) return '未知';

            const date = new Date(timestamp);
            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
        }
    }
}
</script>

<style>
.container {
    padding: 0 0 150rpx 0;
    background-color: #f5f5f5;
}

.section {
    margin: 20rpx;
    background-color: #fff;
    border-radius: 12rpx;
    overflow: hidden;
}

.section-header {
    padding: 20rpx;
    border-bottom: 1rpx solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.section-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
}

.header-actions {
    font-size: 26rpx;
    color: #186BFF;
}

/* 员工信息样式 */
.employee-info {
    padding: 20rpx;
    display: flex;
    align-items: center;
}

.avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 40rpx;
    margin-right: 20rpx;
}

.info-content {
    flex: 1;
}

.name {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 10rpx;
}

.status {
    font-size: 24rpx;
    color: #52c41a;
    background-color: rgba(82, 196, 26, 0.1);
    padding: 4rpx 10rpx;
    border-radius: 4rpx;
    display: inline-block;
}

.status.dismissed {
    color: #f5222d;
    background-color: rgba(245, 34, 45, 0.1);
}

/* 员工选择器样式 */
.employee-selector {
    padding: 20rpx;
}

.employee-item {
    display: flex;
    align-items: center;
    padding: 20rpx;
    background-color: #f5f5f5;
    border-radius: 8rpx;
    margin-bottom: 10rpx;
    position: relative;
}

.employee-item.selected {
    background-color: rgba(24, 144, 255, 0.1);
    border: 1rpx solid #186BFF;
}

.select-icon {
    position: absolute;
    right: 20rpx;
    color: #186BFF;
    font-size: 40rpx;
}

/* 用户列表样式 */
.users-list {
    padding: 20rpx;
}

.user-item {
    display: flex;
    align-items: center;
    padding: 20rpx;
    background-color: #fff;
    border-bottom: 1rpx solid #f0f0f0;
}

.select-box {
    margin-right: 20rpx;
}

.checkbox {
    width: 36rpx;
    height: 36rpx;
    border-radius: 4rpx;
    border: 1rpx solid #d9d9d9;
    display: flex;
    align-items: center;
    justify-content: center;
}

.checkbox.checked {
    background-color: #186BFF;
    border-color: #186BFF;
    position: relative;
}

.checkbox.checked::after {
    content: '';
    width: 20rpx;
    height: 10rpx;
    border-left: 2rpx solid #fff;
    border-bottom: 2rpx solid #fff;
    transform: rotate(-45deg) translate(2rpx, -2rpx);
    position: absolute;
}

.user-info {
    flex: 1;
}

.member-since {
    font-size: 24rpx;
    color: #999;
    margin-top: 8rpx;
}

.empty-state {
    padding: 60rpx 0;
    text-align: center;
    color: #999;
    font-size: 28rpx;
}

/* 底部操作栏 */
.footer-actions {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #fff;
    padding: 20rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.selected-count {
    font-size: 26rpx;
    color: #666;
}

.transfer-btn {
    background-color: #186BFF;
    color: #fff;
    font-size: 28rpx;
    padding: 20rpx 60rpx;
    border-radius: 40rpx;
}

.transfer-btn[disabled] {
    background-color: #ccc;
}

/* 弹窗样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.modal-content {
    width: 80%;
    background-color: #fff;
    border-radius: 16rpx;
    overflow: hidden;
}

.modal-header {
    padding: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
    font-size: 32rpx;
    font-weight: bold;
}

.close-btn {
    font-size: 40rpx;
    color: #999;
}

.modal-body {
    padding: 30rpx;
}

.modal-text {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 30rpx;
    text-align: center;
}

.modal-btns {
    display: flex;
    justify-content: space-between;
}

.modal-btn {
    flex: 1;
    padding: 15rpx 0;
    margin: 0 10rpx;
    font-size: 28rpx;
    border-radius: 8rpx;
}

.modal-btn.cancel {
    background-color: #f5f5f5;
    color: #333;
}

.modal-btn.confirm {
    background-color: #186BFF;
    color: #fff;
}
</style>