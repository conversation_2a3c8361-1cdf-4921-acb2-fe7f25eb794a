import{_ as t,v as e,M as o,q as i,u as a,V as s,aA as l,a as n,E as d,aB as c,U as r,A as h,h as u,w as m,c as f,k as g,F as b,i as v,o as p,j as I,t as _,P as D,l as w,C as y}from"./index-EyT8-w9G.js";import{g as V}from"./batch.CkdUYTDa.js";import{m as k}from"./media-common.B63tJHAk.js";import"./formatter.ClY2FBgV.js";const C=t({mixins:[k],data:()=>({batchId:null,batch:null,batchVideos:[],showActionMenu:!1,showDeleteDialog:!1}),onLoad(t){console.log("batch-detail.vue onLoad 接收到的参数:",t),t.id?(this.batchId=t.id,console.log("设置的 batchId:",this.batchId),this.fetchBatchDetails()):(console.error("未接收到有效的批次ID:",t),e({title:"无效的批次ID",icon:"none",duration:2e3}),setTimeout((()=>o()),1500))},methods:{async fetchBatchDetails(){var t,o,s;try{console.log("fetchBatchDetails 开始加载，batchId:",this.batchId),i({title:"加载中..."});const e=await V(this.batchId);if(console.log("getBatchDetail API 响应:",e),console.log("API 返回的批次数据详情:",e.data),console.log("API 返回的批次ID:",null==(t=e.data)?void 0:t.id),console.log("API 返回的批次名称:",null==(o=e.data)?void 0:o.name),console.log("API 返回的批次标题:",null==(s=e.data)?void 0:s.title),!e.success||!e.data)throw new Error(e.msg||"获取批次详情失败");{const t=e.data;this.batch={id:t.id,batchId:`B${t.id}`,title:t.name||t.title,status:this.mapBatchStatus(t.status),createTime:this.formatDate(t.createTime),startTime:this.formatDate(t.startTime),endTime:this.formatDate(t.endTime),creator:t.creatorName||"未知",videoCount:1,totalViews:t.currentParticipants||0,participants:t.currentParticipants||0,totalReward:t.rewardAmount||0,redPacketAmount:t.redPacketAmount||0,description:t.description||"",videoId:t.videoId,videoTitle:t.videoTitle,videoDescription:t.videoDescription,videoCoverUrl:t.videoCoverUrl,videoUrl:t.videoUrl,videoDuration:t.videoDuration,questions:t.questions||[],statistics:t.statistics||{}},console.log("设置的批次信息:",this.batch),console.log("批次标题:",this.batch.title),console.log("批次描述:",this.batch.description),this.loadBatchVideos()}a()}catch(l){a(),e({title:"加载失败",icon:"none"})}},mapBatchStatus:t=>({0:"pending",1:"active",2:"ended",3:"paused"}[t]||"pending"),loadBatchVideos(){this.batch.videoId?(this.batchVideos=[{id:this.batch.videoId,title:this.batch.videoTitle||this.batch.title||"该批次无视频",description:this.batch.videoDescription||"",videoUrl:this.buildCompleteFileUrl(this.batch.videoUrl),thumbnail:this.buildCompleteFileUrl(this.batch.videoCoverUrl)||"/assets/images/video-cover.jpg",duration:this.batch.videoDuration||0,views:this.batch.totalViews||0,likes:0,comments:0}],console.log("构造的视频数据:",this.batchVideos[0])):(this.batchVideos=[],console.log("批次中没有视频数据"))},goBack(){o()},viewRealTimeData(){s({url:`/pages/admin/media/batch-realtime?id=${this.batchId}`})},showAddVideoModal(){e({title:"添加视频功能开发中",icon:"none"})},viewVideoDetail(t){s({url:`/pages/admin/media/detail?id=${t.id}`})},showVideoMenu(t){l({itemList:["移出批次","查看详情"],success:o=>{0===o.tapIndex?e({title:"移出批次功能开发中",icon:"none"}):1===o.tapIndex&&this.viewVideoDetail(t)}})},getBatchStatusClass:t=>"ended"===t.status?"status-expired":"pending"===t.status?"status-scheduled":"status-active",getBatchStatusText:t=>"ended"===t.status?"已结束":"pending"===t.status?"未开始":"进行中",handleVideoError(t){console.error("视频播放错误:",t.detail);let o="视频加载失败";t.detail&&t.detail.errMsg&&(o+=`: ${t.detail.errMsg}`),this.batchVideos.length>0&&(console.log("视频URL:",this.batchVideos[0].videoUrl),this.batchVideos[0].videoUrl||(this.batchVideos[0].videoUrl="/static/videos/sample.mp4",console.log("使用备用视频URL:",this.batchVideos[0].videoUrl),o="原视频缺失，已替换为示例视频")),e({title:o,icon:"none",duration:3e3})},getVideoShareUrl(){try{let e=null;try{e=n.getLoginInfo(),console.log("adminAuthService.getLoginInfo():",e)}catch(t){console.log("adminAuthService 不可用，从存储获取:",t),e=d("adminLoginInfo"),console.log("uni.getStorageSync(adminLoginInfo):",e)}if(console.log("最终获取的 loginInfo:",e),console.log("loginInfo.userId:",null==e?void 0:e.userId),console.log("loginInfo.username:",null==e?void 0:e.username),console.log("loginInfo.id:",null==e?void 0:e.id),console.log("loginInfo.employeeId:",null==e?void 0:e.employeeId),!e)return console.error("管理员信息缺失:",{loginInfo:e}),"获取链接失败：管理员信息缺失";const o=e.userId||e.username||e.id||e.employeeId||"admin";if(console.log("使用的 sharerId:",o),!this.batch||!this.batch.videoId||!this.batchId)return"获取链接失败：批次信息不完整";const i=c();return console.log("当前配置的UIProjectUrl:",i),console.log("window.APP_CONFIG:",window.APP_CONFIG),`${i}/#/pages/video/index?videoId=${this.batch.videoId}&batchId=${this.batchId}&sharerId=${o}`}catch(t){return console.error("获取分享链接失败:",t),this.$toast(t.message||"获取链接失败"),`配置错误：${t.message}`}},copyVideoLink(){try{const t=this.getVideoShareUrl();if(t.includes("获取链接失败"))return void e({title:t,icon:"none",duration:3e3});i({title:"复制中..."}),r({data:t,success:()=>{a(),e({title:"分享链接已复制到剪贴板",icon:"success",duration:2e3}),console.log("分享链接已复制:",t)},fail:t=>{a(),console.error("复制到剪贴板失败:",t),e({title:"复制失败，请手动复制链接",icon:"none",duration:3e3})}})}catch(t){a(),console.error("复制链接失败:",t),e({title:"复制失败，请重试",icon:"none",duration:3e3})}},viewBatchData(){s({url:`/pages/admin/media/batch-data?id=${this.batchId}`})},showActionMenuHandler(){this.showActionMenu=!0},hideActionMenu(){this.showActionMenu=!1},getQuizCount(){return this.batch.questions&&Array.isArray(this.batch.questions)?this.batch.questions.length:0},showDeleteConfirm(){this.hideActionMenu(),this.showDeleteDialog=!0},hideDeleteConfirm(){this.showDeleteDialog=!1},deleteBatch(){this.hideDeleteConfirm(),e({title:"批次已删除",icon:"success"}),setTimeout((()=>{o()}),1500)}}},[["render",function(t,e,o,i,a,s){const l=D,n=w,d=v,c=y;return p(),h(b,null,[u(d,{class:"page-container"},{default:m((()=>[u(d,{class:"main-layout"},{default:m((()=>[u(d,{class:"content-main"},{default:m((()=>[a.batchVideos.length>0?(p(),f(d,{key:0,class:"video-card"},{default:m((()=>[u(d,{class:"video-player-container"},{default:m((()=>[u(l,{src:a.batchVideos[0].videoUrl||"/assets/videos/sample.mp4",poster:a.batchVideos[0].thumbnail,controls:"",class:"video-player",onError:s.handleVideoError},null,8,["src","poster","onError"]),a.batchVideos[0].videoUrl?g("",!0):(p(),f(d,{key:0,class:"video-overlay"},{default:m((()=>[u(n,{class:"overlay-text"},{default:m((()=>[I("视频加载中...")])),_:1})])),_:1}))])),_:1}),u(d,{class:"video-info"},{default:m((()=>[u(d,{class:"video-header"},{default:m((()=>[u(n,{class:"video-title"},{default:m((()=>[I(_(a.batch.title),1)])),_:1})])),_:1}),a.batch.description?(p(),f(d,{key:0,class:"video-description"},{default:m((()=>[u(n,{class:"description-icon"},{default:m((()=>[I("ℹ️")])),_:1}),u(n,{class:"description-text"},{default:m((()=>[I(_(a.batch.description),1)])),_:1})])),_:1})):g("",!0),u(d,{class:"link-info-section"},{default:m((()=>[u(d,{class:"link-info-header"},{default:m((()=>[u(n,{class:"link-icon"},{default:m((()=>[I("�")])),_:1}),u(n,{class:"link-title"},{default:m((()=>[I("分享链接")])),_:1})])),_:1}),u(d,{class:"link-url-display"},{default:m((()=>[u(n,{class:"link-url"},{default:m((()=>[I(_(s.getVideoShareUrl()),1)])),_:1})])),_:1})])),_:1}),u(d,{class:"action-buttons"},{default:m((()=>[u(d,{class:"button-row"},{default:m((()=>[u(c,{class:"action-btn copy-link-btn",onClick:s.copyVideoLink},{default:m((()=>[u(n,{class:"btn-text"},{default:m((()=>[I("复制链接")])),_:1})])),_:1},8,["onClick"]),u(c,{class:"action-btn view-data-btn",onClick:s.viewBatchData},{default:m((()=>[u(n,{class:"btn-text"},{default:m((()=>[I("查看数据")])),_:1})])),_:1},8,["onClick"])])),_:1}),u(d,{class:"button-row full-width"},{default:m((()=>[u(c,{class:"delete-btn danger",onClick:s.showDeleteConfirm},{default:m((()=>[u(n,{class:"btn-text"},{default:m((()=>[I("删除批次")])),_:1})])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})])),_:1})):g("",!0)])),_:1})])),_:1}),0===a.batchVideos.length?(p(),f(d,{key:0,class:"empty-state"},{default:m((()=>[u(d,{class:"empty-icon"},{default:m((()=>[u(n,{class:"iconfont icon-video-off"})])),_:1}),u(n,{class:"empty-title"},{default:m((()=>[I("该批次暂无视频")])),_:1}),u(n,{class:"empty-desc"},{default:m((()=>[I("请添加视频内容或选择其他批次")])),_:1})])),_:1})):g("",!0)])),_:1}),a.showDeleteDialog?(p(),f(d,{key:0,class:"modal-overlay"},{default:m((()=>[u(d,{class:"confirm-modal"},{default:m((()=>[u(d,{class:"modal-header"},{default:m((()=>[u(n,{class:"modal-title"},{default:m((()=>[I("确认删除")])),_:1})])),_:1}),u(d,{class:"modal-body"},{default:m((()=>[u(n,{class:"confirm-text"},{default:m((()=>[I('确定要删除批次"'+_(a.batch.title)+'"吗？删除后无法恢复，请谨慎操作。',1)])),_:1})])),_:1}),u(d,{class:"modal-footer"},{default:m((()=>[u(c,{class:"btn secondary",onClick:s.hideDeleteConfirm},{default:m((()=>[I("取消")])),_:1},8,["onClick"]),u(c,{class:"btn danger",onClick:s.deleteBatch},{default:m((()=>[I("确认删除")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})):g("",!0)],64)}],["__scopeId","data-v-72a47b72"]]);export{C as default};
