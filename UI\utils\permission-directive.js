/**
 * 权限指令 - v-permission
 * 用于在模板中直接控制元素的显示/隐藏
 * 
 * 使用方式：
 * v-permission="'admin'" - 检查角色
 * v-permission="['admin', 'manager']" - 检查多个角色
 * v-permission:feature="'create_user'" - 检查功能权限
 * v-permission:level="'manager'" - 检查权限级别
 */

import adminAuthService from './adminAuthService.js'
import { ROLE_PERMISSIONS, PERMISSION_LEVELS, FEATURE_PERMISSIONS } from './role-permission-manager.js'

/**
 * 获取当前用户权限信息
 */
function getCurrentPermissionInfo() {
  const userType = adminAuthService.getUserType()
  const permissions = ROLE_PERMISSIONS[userType] || []
  const level = PERMISSION_LEVELS[userType] || 0

  return {
    userType,
    permissions,
    level
  }
}

/**
 * 检查角色权限
 * @param {string|Array} roles - 角色或角色数组
 * @param {Object} permissionInfo - 权限信息
 * @returns {boolean}
 */
function checkRolePermission(roles, permissionInfo) {
  if (!permissionInfo.userType) return false
  
  if (Array.isArray(roles)) {
    return roles.includes(permissionInfo.userType)
  }
  
  return permissionInfo.userType === roles
}

/**
 * 检查功能权限
 * @param {string} feature - 功能名称
 * @param {Object} permissionInfo - 权限信息
 * @returns {boolean}
 */
function checkFeaturePermission(feature, permissionInfo) {
  if (!permissionInfo.userType) return false
  
  const allowedRoles = PERMISSION_CONFIG.FEATURE_PERMISSIONS[feature]
  if (!allowedRoles) return false
  
  return allowedRoles.includes(permissionInfo.userType)
}

/**
 * 检查权限级别
 * @param {string} requiredRole - 需要的最低角色
 * @param {Object} permissionInfo - 权限信息
 * @returns {boolean}
 */
function checkPermissionLevel(requiredRole, permissionInfo) {
  if (!permissionInfo.userType) return false
  
  const requiredLevel = PERMISSION_CONFIG.PERMISSION_LEVELS[requiredRole] || 0
  return permissionInfo.level >= requiredLevel
}

/**
 * 权限指令定义
 */
export const permissionDirective = {
  /**
   * 指令绑定时执行
   */
  bind(el, binding, vnode) {
    const { value, arg, modifiers } = binding
    const permissionInfo = getCurrentPermissionInfo()
    
    let hasPermission = false
    
    // 根据参数类型执行不同的权限检查
    if (arg === 'feature') {
      // 检查功能权限
      hasPermission = checkFeaturePermission(value, permissionInfo)
    } else if (arg === 'level') {
      // 检查权限级别
      hasPermission = checkPermissionLevel(value, permissionInfo)
    } else {
      // 默认检查角色权限
      hasPermission = checkRolePermission(value, permissionInfo)
    }
    
    // 如果没有权限，隐藏元素
    if (!hasPermission) {
      // 保存原始显示状态
      el._originalDisplay = el.style.display
      el.style.display = 'none'
      
      // 如果使用了 remove 修饰符，直接移除元素
      if (modifiers.remove && el.parentNode) {
        el._originalParent = el.parentNode
        el._originalNextSibling = el.nextSibling
        el.parentNode.removeChild(el)
      }
    }
  },

  /**
   * 指令更新时执行
   */
  update(el, binding, vnode) {
    const { value, arg, modifiers } = binding
    const permissionInfo = getCurrentPermissionInfo()
    
    let hasPermission = false
    
    // 根据参数类型执行不同的权限检查
    if (arg === 'feature') {
      hasPermission = checkFeaturePermission(value, permissionInfo)
    } else if (arg === 'level') {
      hasPermission = checkPermissionLevel(value, permissionInfo)
    } else {
      hasPermission = checkRolePermission(value, permissionInfo)
    }
    
    // 根据权限状态显示/隐藏元素
    if (hasPermission) {
      // 有权限，显示元素
      if (modifiers.remove && el._originalParent) {
        // 如果之前被移除，重新插入
        if (el._originalNextSibling) {
          el._originalParent.insertBefore(el, el._originalNextSibling)
        } else {
          el._originalParent.appendChild(el)
        }
      } else {
        // 恢复显示
        el.style.display = el._originalDisplay || ''
      }
    } else {
      // 没有权限，隐藏元素
      if (modifiers.remove && el.parentNode) {
        el._originalParent = el.parentNode
        el._originalNextSibling = el.nextSibling
        el.parentNode.removeChild(el)
      } else {
        el._originalDisplay = el.style.display
        el.style.display = 'none'
      }
    }
  },

  /**
   * 指令解绑时执行
   */
  unbind(el, binding) {
    // 清理保存的引用
    delete el._originalDisplay
    delete el._originalParent
    delete el._originalNextSibling
  }
}

/**
 * 安装权限指令
 * @param {Object} Vue - Vue实例
 */
export function installPermissionDirective(Vue) {
  Vue.directive('permission', permissionDirective)
}

/**
 * 权限检查工具函数（供JS中使用）
 */
export const permissionUtils = {
  /**
   * 检查是否有指定角色
   */
  hasRole(roles) {
    const permissionInfo = getCurrentPermissionInfo()
    return checkRolePermission(roles, permissionInfo)
  },

  /**
   * 检查是否有指定功能权限
   */
  hasFeature(feature) {
    const permissionInfo = getCurrentPermissionInfo()
    return checkFeaturePermission(feature, permissionInfo)
  },

  /**
   * 检查是否有指定权限级别
   */
  hasLevel(requiredRole) {
    const permissionInfo = getCurrentPermissionInfo()
    return checkPermissionLevel(requiredRole, permissionInfo)
  },

  /**
   * 获取当前用户类型
   */
  getCurrentUserType() {
    return adminAuthService.getUserType()
  },

  /**
   * 获取当前用户权限列表
   */
  getCurrentPermissions() {
    const userType = adminAuthService.getUserType()
    return PERMISSION_CONFIG.ROLE_PERMISSIONS[userType] || []
  }
}

export default {
  install: installPermissionDirective,
  utils: permissionUtils
}
