/**
 * 权限管理工具
 * 处理基于用户角色的数据访问控制
 * 注意：此文件已被新的权限系统替代，建议使用 permission-mixin.js 和 permission-directive.js
 */
import { showError } from './toast-manager.js';
import { USER_ROLES, PERMISSION_LEVELS, ROLE_PERMISSIONS, FEATURE_PERMISSIONS } from './role-permission-manager.js';

// 使用统一的权限配置
export { USER_ROLES, PERMISSION_LEVELS };

/**
 * 权限管理器类
 */
export class PermissionManager {
  constructor() {
    this.currentUser = null;
    this.currentRole = null;
  }

  /**
   * 设置当前用户信息
   * @param {Object} user - 用户信息
   * @param {string} user.id - 用户ID
   * @param {string} user.role - 用户角色
   * @param {string} user.username - 用户名
   */
  setCurrentUser (user) {
    this.currentUser = user;
    this.currentRole = user.role;
  }

  /**
   * 获取当前用户信息
   * @returns {Object|null} 当前用户信息
   */
  getCurrentUser () {
    return this.currentUser;
  }

  /**
   * 获取当前用户角色
   * @returns {string|null} 当前用户角色
   */
  getCurrentRole () {
    return this.currentRole;
  }

  /**
   * 检查是否有指定权限
   * @param {string} requiredRole - 需要的角色
   * @returns {boolean} 是否有权限
   */
  hasPermission (requiredRole) {
    if (!this.currentRole) return false;

    const currentLevel = PERMISSION_LEVELS[this.currentRole] || 0;
    const requiredLevel = PERMISSION_LEVELS[requiredRole] || 0;

    return currentLevel >= requiredLevel;
  }

  /**
   * 检查是否可以查看指定用户的数据
   * @param {Object} targetUser - 目标用户
   * @returns {boolean} 是否可以查看
   */
  canViewUser (targetUser) {
    if (!this.currentUser || !targetUser) return false;

    // 超管可以查看所有用户
    if (this.currentRole === USER_ROLES.SUPER_ADMIN) return true;

    // 管理员可以查看所有用户
    if (this.currentRole === USER_ROLES.ADMIN) return true;

    // 管理可以查看自己的下级员工和用户
    if (this.currentRole === USER_ROLES.MANAGER || this.currentRole === USER_ROLES.AGENT) {
      // 如果目标是员工，检查是否是自己的下级
      if (targetUser.role === USER_ROLES.EMPLOYEE) {
        return targetUser.managerId === this.currentUser.id;
      }
      // 如果目标是用户，检查是否在自己管理范围内
      if (targetUser.role === USER_ROLES.USER) {
        return this.isUserInManagementScope(targetUser);
      }
    }

    // 员工只能查看自己管理的用户
    if (this.currentRole === USER_ROLES.EMPLOYEE) {
      if (targetUser.role === USER_ROLES.USER) {
        return targetUser.employeeId === this.currentUser.id;
      }
    }

    // 用户只能查看自己
    if (this.currentRole === USER_ROLES.USER) {
      return targetUser.id === this.currentUser.id;
    }

    return false;
  }

  /**
   * 检查是否可以管理指定用户
   * @param {Object} targetUser - 目标用户
   * @returns {boolean} 是否可以管理
   */
  canManageUser (targetUser) {
    if (!this.currentUser || !targetUser) return false;

    // 超管可以管理所有用户
    if (this.currentRole === USER_ROLES.SUPER_ADMIN) return true;

    // 管理员可以管理除超管外的所有用户
    if (this.currentRole === USER_ROLES.ADMIN) {
      return targetUser.role !== USER_ROLES.SUPER_ADMIN;
    }

    // 管理可以管理自己的下级员工
    if (this.currentRole === USER_ROLES.MANAGER || this.currentRole === USER_ROLES.AGENT) {
      if (targetUser.role === USER_ROLES.EMPLOYEE) {
        return targetUser.managerId === this.currentUser.id;
      }
    }

    // 其他角色不能管理用户
    return false;
  }

  /**
   * 检查用户是否在管理范围内
   * @param {Object} user - 用户信息
   * @returns {boolean} 是否在管理范围内
   */
  isUserInManagementScope (user) {
    // 这里需要根据实际业务逻辑实现
    // 例如：检查用户是否属于当前管理的员工管理的用户
    return true; // 简化实现
  }

  /**
   * 获取用户可以访问的数据范围
   * @returns {Object} 数据访问范围配置
   */
  getDataAccessScope () {
    const scope = {
      canViewAllManagers: false,
      canViewAllEmployees: false,
      canViewAllUsers: false,
      canManageEmployees: false,
      canManageUsers: false,
      managerId: null,
      employeeId: null
    };

    switch (this.currentRole) {
      case USER_ROLES.SUPER_ADMIN:
      case USER_ROLES.ADMIN:
        scope.canViewAllManagers = true;
        scope.canViewAllEmployees = true;
        scope.canViewAllUsers = true;
        scope.canManageEmployees = true;
        scope.canManageUsers = true;
        break;

      case USER_ROLES.MANAGER:
      case USER_ROLES.AGENT:
        scope.canViewAllEmployees = false; // 只能查看自己的下级
        scope.canViewAllUsers = false; // 只能查看自己管理范围内的用户
        scope.canManageEmployees = true; // 可以管理自己的下级员工
        scope.managerId = this.currentUser.id;
        break;

      case USER_ROLES.EMPLOYEE:
        scope.canViewAllUsers = false; // 只能查看自己管理的用户
        scope.employeeId = this.currentUser.id;
        break;

      case USER_ROLES.USER:
        // 用户只能查看自己的信息
        break;
    }

    return scope;
  }

  /**
   * 过滤数据列表，只返回用户有权限查看的数据
   * @param {Array} dataList - 数据列表
   * @param {string} dataType - 数据类型 ('managers', 'employees', 'users')
   * @returns {Array} 过滤后的数据列表
   */
  filterDataByPermission (dataList, dataType) {
    if (!Array.isArray(dataList)) return [];

    const scope = this.getDataAccessScope();

    switch (dataType) {
      case 'managers':
        return scope.canViewAllManagers ? dataList : [];

      case 'employees':
        if (scope.canViewAllEmployees) {
          return dataList;
        } else if (scope.managerId) {
          return dataList.filter(emp => emp.managerId === scope.managerId);
        }
        return [];

      case 'users':
        if (scope.canViewAllUsers) {
          return dataList;
        } else if (scope.managerId) {
          // 管理可以查看自己管理范围内的用户
          return dataList.filter(user => this.isUserInManagementScope(user));
        } else if (scope.employeeId) {
          // 员工只能查看自己管理的用户
          return dataList.filter(user => user.employeeId === scope.employeeId);
        }
        return [];

      default:
        return dataList;
    }
  }

  /**
   * 检查是否可以执行指定操作
   * @param {string} action - 操作类型
   * @param {Object} target - 目标对象
   * @returns {boolean} 是否可以执行
   */
  canPerformAction (action, target = null) {
    const scope = this.getDataAccessScope();

    switch (action) {
      case 'create_manager':
        return this.hasPermission(USER_ROLES.ADMIN);

      case 'create_employee':
        return scope.canManageEmployees;

      case 'create_user':
        return scope.canManageUsers;

      case 'disable_account':
      case 'enable_account':
        return this.canManageUser(target);

      case 'reset_password':
        return this.canManageUser(target);

      case 'view_statistics':
        return this.hasPermission(USER_ROLES.EMPLOYEE);

      default:
        return false;
    }
  }
}

// 创建全局权限管理器实例
export const permissionManager = new PermissionManager();

/**
 * 权限检查装饰器
 * @param {string} requiredRole - 需要的角色
 * @returns {Function} 装饰器函数
 */
export function requirePermission (requiredRole) {
  return function (target, propertyName, descriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = function (...args) {
      if (!permissionManager.hasPermission(requiredRole)) {
        showError('权限不足');
        return;
      }

      return originalMethod.apply(this, args);
    };

    return descriptor;
  };
}

/**
 * 初始化权限管理器
 * @param {Object} userInfo - 用户信息
 */
export function initPermissionManager (userInfo) {
  permissionManager.setCurrentUser(userInfo);
}

/**
 * 获取当前用户的权限范围
 * @returns {Object} 权限范围
 */
export function getCurrentPermissionScope () {
  return permissionManager.getDataAccessScope();
}

/**
 * 检查当前用户是否有指定权限
 * @param {string} requiredRole - 需要的角色
 * @returns {boolean} 是否有权限
 */
export function hasCurrentPermission (requiredRole) {
  return permissionManager.hasPermission(requiredRole);
}
