<template>
  <view class="container">
    <!-- 直接使用 uni-app 原生 video 组件 -->
    <video id="myVideo" ref="videoPlayer" :src="currentVideo.url" :poster="currentVideo.cover" class="video-player"
      :controls="true" :autoplay="true" :show-progress="true" :show-fullscreen-btn="true" :show-play-btn="true"
      :show-center-play-btn="true" :enable-progress-gesture="false" :page-gesture="false" :direction="0"
      :show-mute-btn="false" :enable-play-gesture="false" @loadedmetadata="onLoadedMetadata" @timeupdate="onTimeUpdate"
      @ended="onVideoEnded" @play="onPlay" @pause="onPause" @fullscreenchange="onFullscreenChange" @waiting="onWaiting"
      @canplay="onCanPlay" @seeking="onSeeking" @seeked="onSeeked"></video>

    <!-- 内容区域 - 仅在非全屏时显示 -->
    <view v-if="!isFullscreen" class="video-content">
      <!-- 视频信息 -->
      <view class="video-info">
        <text class="video-title">{{ currentVideo.title }}</text>
        <view class="video-meta">
          <text class="views">{{ currentVideo.views }}次观看</text>
        </view>
        <text class="description">{{ currentVideo.description }}</text>
      </view>

      <!-- 问答区域 -->
      <VideoQuiz :questions="quizData.questions" :rewardAmount="currentVideo.rewardAmount || 0"
        :videoCompleted="videoCompleted" @submit="onQuizSubmit" @complete="onQuizComplete" />
    </view>

    <!-- 用户选择器弹窗 -->
    <view class="user-selector-modal" v-if="showUserSelector" @click="closeUserSelector">
      <view class="user-selector-content" @click.stop>
        <view class="selector-header">
          <text class="selector-title">选择测试用户</text>
          <text class="selector-subtitle">请选择一个用户身份进行测试</text>
        </view>

        <scroll-view class="user-list" scroll-y>
          <view class="user-item" v-for="user in availableUsers" :key="user.id" @click="selectUser(user)">
            <image class="user-avatar" :src="user.avatar" mode="aspectFill"></image>
            <view class="user-info">
              <text class="user-nickname">{{ user.nickname }}</text>
              <text class="user-id">ID: {{ user.id }}</text>
            </view>
            <view class="select-icon">
              <text class="icon">></text>
            </view>
          </view>
        </scroll-view>

        <view class="selector-footer">
          <button class="random-btn" @click="selectRandomUser">随机选择</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>

import VideoQuiz from "../../components/VideoQuiz.vue";
// 保留旧API作为备用
import { createViewRecord, updateViewProgress } from "@/api/view-record.js";
// 导入媒体文件处理mixin
import mediaCommonMixin from "@/mixins/media-common.js";
// 导入配置工具
import { getApiBaseURL } from "@/utils/config-manager.js";
// 导入微信用户认证服务
import wechatUserService from "@/utils/wechatUserService.js";

export default {
  mixins: [mediaCommonMixin],
  components: {
    VideoQuiz,
  },
  data () {
    return {
      // 用户选择相关
      showUserSelector: false, // 是否显示用户选择器
      selectedUserId: null, // 选中的用户ID
      availableUsers: [
        { id: '1', nickname: '小明同学', avatar: 'http://*************/static/images/avatar-placeholder.png' },
        { id: '2', nickname: '阳光女孩', avatar: 'http://*************/static/images/avatar-placeholder.png' },
        { id: '3', nickname: '健康达人', avatar: 'http://*************/static/images/avatar-placeholder.png' },
        { id: '4', nickname: '学习小能手', avatar: 'http://*************/static/images/avatar-placeholder.png' },
        { id: '5', nickname: '运动爱好者', avatar: 'http://*************/static/images/avatar-placeholder.png' },
        { id: '9', nickname: '张小明', avatar: '/avatars/avatar1.jpg' },
        { id: '10', nickname: '李小红', avatar: '/avatars/avatar2.jpg' },
        { id: '11', nickname: '王小刚', avatar: '/avatars/avatar3.jpg' },
        { id: '12', nickname: '赵小美', avatar: '/avatars/avatar4.jpg' },
        { id: '13', nickname: '刘小强', avatar: '/avatars/avatar5.jpg' },
        { id: '14', nickname: '陈小丽', avatar: '/avatars/avatar6.jpg' },
        { id: '15', nickname: '周小华', avatar: '/avatars/avatar7.jpg' },
        { id: '16', nickname: '吴小军', avatar: '/avatars/avatar8.jpg' },
        { id: '17', nickname: '郑小芳', avatar: '/avatars/avatar9.jpg' },
        { id: '18', nickname: '孙小龙', avatar: '/avatars/avatar10.jpg' },
        { id: '20', nickname: '知识青年', avatar: 'http://*************/static/images/avatar-placeholder.png' },
        { id: '21', nickname: '安全专家', avatar: 'http://*************/static/images/avatar-placeholder.png' },
        { id: '22', nickname: '生活达人', avatar: 'http://*************/static/images/avatar-placeholder.png' },
        { id: '23', nickname: '健康守护者', avatar: 'http://*************/static/images/avatar-placeholder.png' },
        { id: '24', nickname: '防护专员', avatar: 'http://*************/static/images/avatar-placeholder.png' },
        { id: '25', nickname: '消毒小能手', avatar: 'http://*************/static/images/avatar-placeholder.png' },
        { id: '26', nickname: '卫生监督员', avatar: 'http://*************/static/images/avatar-placeholder.png' }
      ],

      // 视频数据
      videoId: 2, // 默认视频ID
      batchId: null, // 批次ID
      sharerId: null, // 分享人ID
      currentVideo: {}, // 当前视频信息
      quizData: {}, // 问题数据
      duration: 0, // 视频总时长

      // 播放状态
      isFullscreen: false,
      videoCompleted: false,
      maxWatchTime: 0, // 已观看的最大时间点
      currentPlayTime: 0, // 当前播放时间

      // 观看进度监控
      progressTimer: null, // 进度监控定时器
      watchStartTime: null, // 开始观看时间
      totalWatchTime: 0, // 总观看时长
      viewRecordId: null, // 观看记录ID
      recordCreated: false, // 标记观看记录是否已创建
      isCreatingRecord: false, // 防止并发创建记录

      // 批次信息
      batchInfo: null, // 批次详情信息
    };
  },

  async onLoad (options) {


    // 如果options为空，尝试从URL中解析参数
    if (!options || Object.keys(options).length === 0) {
      options = this.parseUrlParams() || {};
    }

    // 初始化模拟微信用户（临时方案）
    try {

      this.initMockWechatUser();
    } catch (error) {
      console.error('模拟用户初始化失败:', error);
      // 继续执行，不阻断视频播放
    }

    // 页面加载时禁用滚动
    uni.setPageMeta({
      pageStyle: {
        overflow: "hidden",
      },
    });

    // 获取URL参数
    if (options && (options.id || options.videoId)) {
      this.videoId = parseInt(options.id || options.videoId);

    }
    if (options && options.batchId) {
      this.batchId = parseInt(options.batchId);

    }
    if (options && options.sharerId) {
      this.sharerId = options.sharerId; // sharerId可能是字符串，不需要parseInt

    }



    // 显示用户选择器
    this.showUserSelectorModal();
  },

  onShow () {

    // 如果参数为空，尝试重新解析
    if (!this.batchId) {
      this.parseUrlParams();
    }

    // 页面显示时恢复播放
    const videoContext = uni.createVideoContext('myVideo', this)
    if (videoContext) {
      videoContext.play()
    }
    // 恢复进度监控
    this.startProgressMonitoring();
  },

  onHide () {
    // 页面隐藏时暂停播放
    const videoContext = uni.createVideoContext('myVideo', this)
    if (videoContext) {
      videoContext.pause()
    }
    // 停止进度监控
    this.stopProgressMonitoring();
  },

  onUnload () {
    // 页面卸载时清理定时器，但保留记录状态
    this.stopProgressMonitoring();
    // 不重置 recordCreated，保持记录状态
  },

  methods: {
    // ===== 本地存储管理 =====
    // 获取本地观看记录
    getLocalWatchRecord () {
      if (!this.batchId || !this.getCurrentUserId()) return null;

      const key = `watch_record_${this.batchId}_${this.getCurrentUserId()}`;
      const record = uni.getStorageSync(key);

      // 检查记录是否过期（24小时）
      if (record && record.expireTime && Date.now() > record.expireTime) {
        uni.removeStorageSync(key);
        return null;
      }

      return record;
    },

    // 保存本地观看记录
    saveLocalWatchRecord (recordData) {
      if (!this.batchId || !this.getCurrentUserId()) return;

      const key = `watch_record_${this.batchId}_${this.getCurrentUserId()}`;
      const record = {
        id: recordData.id,
        batchId: this.batchId,
        userId: this.getCurrentUserId(),
        createTime: Date.now(),
        expireTime: Date.now() + 24 * 60 * 60 * 1000 // 24小时过期
      };

      uni.setStorageSync(key, record);
    },

    // 清除本地观看记录
    clearLocalWatchRecord () {
      if (!this.batchId || !this.getCurrentUserId()) return;

      const key = `watch_record_${this.batchId}_${this.getCurrentUserId()}`;
      uni.removeStorageSync(key);
    },

    // ===== 无认证API调用 =====
    async getVideoDetailWithoutAuth (videoId) {
      // 直接调用uni.request，不经过认证拦截器
      return new Promise((resolve, reject) => {
        uni.request({
          url: `${getApiBaseURL()}/Video/Get/${videoId}`,
          method: 'GET',
          header: {
            'Content-Type': 'application/json'
          },
          success: (res) => {
            if (res.statusCode === 200) {
              resolve(res.data);
            } else {
              reject(new Error(`HTTP ${res.statusCode}`));
            }
          },
          fail: (err) => {
            reject(err);
          }
        });
      });
    },

    async getBatchDetailWithoutAuth (batchId) {
      // 直接调用uni.request，不经过认证拦截器
      return new Promise((resolve, reject) => {
        uni.request({
          url: `${getApiBaseURL()}/Batch/${batchId}`,
          method: 'GET',
          header: {
            'Content-Type': 'application/json'
          },
          success: (res) => {
            if (res.statusCode === 200) {
              resolve(res.data);
            } else {
              reject(new Error(`HTTP ${res.statusCode}`));
            }
          },
          fail: (err) => {
            reject(err);
          }
        });
      });
    },

    async getUserVideoProgressWithoutAuth () {
      // 直接返回模拟数据，不需要认证
      return Promise.resolve({
        success: true,
        data: {
          progress: 0,
          playDuration: 0,
          completed: false
        },
        msg: '观看进度获取成功'
      });
    },

    async createOrGetRecordWithoutAuth (data) {
      // 直接调用uni.request，不经过认证拦截器
      return new Promise((resolve, reject) => {
        uni.request({
          url: `${getApiBaseURL()}/UserBatchRecord/create-or-get`,
          method: 'POST',
          header: {
            'Content-Type': 'application/json'
          },
          data: data,
          success: (res) => {
            if (res.statusCode === 200) {
              resolve(res.data);
            } else {
              reject(new Error(`HTTP ${res.statusCode}`));
            }
          },
          fail: (err) => {
            reject(err);
          }
        });
      });
    },

    async updateWatchProgressWithoutAuth (userId, data) {
      // 直接调用uni.request，不经过认证拦截器
      return new Promise((resolve, reject) => {
        uni.request({
          url: `${getApiBaseURL()}/UserBatchRecord/${userId}/watch-progress`,
          method: 'POST',
          header: {
            'Content-Type': 'application/json'
          },
          data: data,
          success: (res) => {
            if (res.statusCode === 200) {
              resolve(res.data);
            } else {
              reject(new Error(`HTTP ${res.statusCode}`));
            }
          },
          fail: (err) => {
            reject(err);
          }
        });
      });
    },

    async startWatchingWithoutAuth (userId, batchId) {
      // 直接调用uni.request，不经过认证拦截器
      return new Promise((resolve, reject) => {
        uni.request({
          url: `${getApiBaseURL()}/UserBatchRecord/${userId}/${batchId}/start-watching`,
          method: 'POST',
          header: {
            'Content-Type': 'application/json'
          },
          success: (res) => {
            if (res.statusCode === 200) {
              resolve(res.data);
            } else {
              reject(new Error(`HTTP ${res.statusCode}`));
            }
          },
          fail: (err) => {
            reject(err);
          }
        });
      });
    },

    async submitAnswerWithoutAuth (userId, data) {
      // 直接调用uni.request，不经过认证拦截器
      return new Promise((resolve, reject) => {
        uni.request({
          url: `${getApiBaseURL()}/UserBatchRecord/${userId}/submit-answer`,
          method: 'POST',
          header: {
            'Content-Type': 'application/json'
          },
          data: data,
          success: (res) => {
            if (res.statusCode === 200) {
              resolve(res.data);
            } else {
              reject(new Error(`HTTP ${res.statusCode}`));
            }
          },
          fail: (err) => {
            reject(err);
          }
        });
      });
    },

    async grantRewardWithoutAuth (userId, data) {
      // 直接调用uni.request，不经过认证拦截器
      return new Promise((resolve, reject) => {
        uni.request({
          url: `${getApiBaseURL()}/UserBatchRecord/${userId}/grant-reward`,
          method: 'POST',
          header: {
            'Content-Type': 'application/json'
          },
          data: data,
          success: (res) => {
            if (res.statusCode === 200) {
              resolve(res.data);
            } else {
              reject(new Error(`HTTP ${res.statusCode}`));
            }
          },
          fail: (err) => {
            reject(err);
          }
        });
      });
    },

    // ===== 用户认证 =====
    async tryWechatAutoLogin (options) {
      try {
        // 使用微信用户服务进行自动登录
        const result = await wechatUserService.tryWechatAutoLogin(options);

        if (result.success) {
          console.log('微信自动登录成功:', result.data.userInfo.nickname);
          return result.data;
        } else {
          throw new Error(result.message || '微信登录失败');
        }
      } catch (error) {
        console.error('微信自动登录失败:', error);
        throw error;
      }
    },

    // ===== 数据加载 =====
    async loadVideoData () {
      try {
        uni.showLoading({
          title: "加载视频中...",
        });

        // 如果有批次ID，优先从批次获取数据（包含视频信息和问题数据）
        if (this.batchId) {
          await this.loadDataFromBatch();
        } else {
          // 没有批次ID时，直接获取视频数据
          await this.loadDataFromVideo();
        }

        // 加载用户观看进度
        await this.loadUserProgress();

        // 如果有分享人ID，记录分享信息
        if (this.sharerId) {
          this.recordSharerInfo();
        }

        uni.hideLoading();
      } catch (error) {
        console.error('加载视频数据失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: error.message || "加载失败",
          icon: "none",
        });

        // API调用失败，返回上一页
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }
    },

    // 从批次获取数据（包含视频信息和问题数据）
    async loadDataFromBatch () {
      console.log('从批次获取数据:', this.batchId);
      const response = await this.getBatchDetailWithoutAuth(this.batchId);

      if (!response.success || !response.data) {
        throw new Error(response.msg || '获取批次详情失败');
      }

      const batch = response.data;
      console.log('=== 批次数据调试信息 ===');
      console.log('API返回的原始批次数据:', batch);

      // 验证批次状态
      this.validateBatchStatus(batch);

      // 设置批次信息
      this.batchInfo = batch;

      // 从批次数据中提取视频信息
      const videoUrl = this.buildCompleteFileUrl(batch.videoUrl);
      const coverUrl = this.buildCompleteFileUrl(batch.videoCoverUrl);

      this.currentVideo = {
        id: batch.videoId,
        title: batch.videoTitle,
        cover: coverUrl || '/assets/images/video-cover.jpg',
        url: videoUrl || 'https://www.runoob.com/try/demo_source/mov_bbb.mp4',
        duration: batch.videoDuration || 0,
        views: 0, // 批次数据中没有观看次数
        likes: 0,
        description: batch.videoDescription || '',
        rewardAmount: batch.rewardAmount || 0
      };

      this.duration = batch.videoDuration || 0;

      // 处理问题数据
      this.processQuizData(batch.questions);

      console.log('最终设置的currentVideo:', this.currentVideo);
      console.log('最终设置的quizData:', this.quizData);
      console.log('=== 批次数据调试结束 ===');
    },

    // 从视频API获取数据
    async loadDataFromVideo () {
      console.log('从视频API获取数据:', this.videoId);
      const response = await this.getVideoDetailWithoutAuth(this.videoId);

      if (!response.success || !response.data) {
        throw new Error(response.msg || '获取视频详情失败');
      }

      const video = response.data;
      console.log('=== 视频数据调试信息 ===');
      console.log('API返回的原始视频数据:', video);

      const videoUrl = this.buildCompleteFileUrl(video.videoUrl);
      const coverUrl = this.buildCompleteFileUrl(video.coverUrl);

      this.currentVideo = {
        id: video.id,
        title: video.title,
        cover: coverUrl || '/assets/images/video-cover.jpg',
        url: videoUrl || 'https://www.runoob.com/try/demo_source/mov_bbb.mp4',
        duration: video.duration || 0,
        views: video.viewCount || 0,
        likes: video.likeCount || 0,
        description: video.description || '',
        rewardAmount: video.rewardAmount || 0
      };

      this.duration = video.duration || 0;

      // 处理问题数据
      this.processQuizData(video.questions);

      console.log('最终设置的currentVideo:', this.currentVideo);
      console.log('最终设置的quizData:', this.quizData);
      console.log('=== 视频数据调试结束 ===');
    },

    // 加载用户观看进度
    async loadUserProgress () {
      try {
        const response = await this.getUserVideoProgressWithoutAuth();

        if (response.success && response.data) {
          const progress = response.data;
          // API返回的是progress(百分比)和playDuration(播放时长)，需要转换
          this.totalWatchTime = progress.playDuration || 0;
          // 根据进度百分比计算maxWatchTime
          this.maxWatchTime = this.duration > 0 ? Math.round((progress.progress / 100) * this.duration) : 0;

          // 如果有观看进度，可以选择是否从上次位置继续播放
          if (this.maxWatchTime > 0) {
            uni.showModal({
              title: '继续观看',
              content: `检测到您上次观看到 ${this.formatTime(this.maxWatchTime)}，是否从此处继续？`,
              success: (res) => {
                if (res.confirm) {
                  // 用户选择继续，跳转到上次观看位置
                  setTimeout(() => {
                    const videoContext = uni.createVideoContext('myVideo', this);
                    if (videoContext) {
                      videoContext.seek(this.maxWatchTime);
                    }
                  }, 1000);
                }
              }
            });
          }
        }
      } catch (error) {
        console.error('加载用户进度失败:', error);
        // 进度加载失败不影响视频播放，只是从头开始
      }
    },

    // 处理问题数据，将API格式转换为组件期望的格式
    processQuizData (apiQuestions) {
      if (!apiQuestions || !Array.isArray(apiQuestions) || apiQuestions.length === 0) {
        console.log('没有问题数据');
        this.quizData = { questions: [] };
        return;
      }

      console.log('开始处理问题数据:', apiQuestions);

      try {
        const questions = apiQuestions.map((apiQuestion) => {
          // 生成选项标识符 A, B, C, D...
          const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];

          // 转换选项格式
          const options = (apiQuestion.options || []).map((apiOption, optionIndex) => ({
            id: optionLabels[optionIndex] || optionIndex.toString(),
            text: apiOption.optionText || apiOption.text || ''
          }));

          // 找到正确答案的标识符
          let correctAnswer = '';
          const correctOptionIndex = (apiQuestion.options || []).findIndex(option => option.isCorrect);
          if (correctOptionIndex >= 0) {
            correctAnswer = optionLabels[correctOptionIndex] || correctOptionIndex.toString();
          }

          return {
            question: apiQuestion.questionText || apiQuestion.question || '',
            options: options,
            correctAnswer: correctAnswer
          };
        });

        this.quizData = { questions };
        console.log('问题数据处理完成:', this.quizData);
      } catch (error) {
        console.error('处理问题数据失败:', error);
        this.quizData = { questions: [] };
      }
    },

    // 验证批次状态
    validateBatchStatus (batch) {
      // 检查批次状态
      if (batch.status !== 1) {
        throw new Error('批次未启用');
      }

      // 检查时间范围
      const now = new Date();
      const startTime = new Date(batch.startTime);
      const endTime = new Date(batch.endTime);

      if (now < startTime) {
        throw new Error('批次尚未开始');
      }

      if (now > endTime) {
        throw new Error('批次已结束');
      }

      console.log('批次验证通过:', batch);
    },



    // 格式化时间显示
    formatTime (seconds) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.floor(seconds % 60);
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    },

    // ===== 视频事件处理 =====
    // 视频时间更新
    onTimeUpdate (e) {
      const detail = e.detail || {}
      const currentTime = detail.currentTime || 0
      const duration = detail.duration || this.duration || 0

      // 更新当前播放时间
      this.currentPlayTime = currentTime;

      // 更新时长
      if (duration > 0) {
        this.duration = duration
      }

      // 防止快进：如果当前时间超过最大观看时间+1秒，则回退到最大观看时间
      if (currentTime > this.maxWatchTime + 1) {
        const videoContext = uni.createVideoContext('myVideo', this)
        if (videoContext) {
          videoContext.seek(this.maxWatchTime);
        }
        uni.showToast({
          title: "请完整观看视频，不允许快进",
          icon: "none",
          duration: 2000
        });
        return; // 阻止后续逻辑执行
      }

      // 更新最大观看时间（只有在没有快进的情况下才更新）
      if (currentTime > this.maxWatchTime) {
        this.maxWatchTime = currentTime;
      }

      // 发送时间更新事件给父组件的其他逻辑
      const data = {
        current: currentTime,
        duration: duration
      }

      // 调用原有的进度报告逻辑
      if (this.reportProgress) {
        this.reportProgress(data)
      }
    },

    // 视频播放完成
    onVideoEnded () {
      this.videoCompleted = true;
      this.maxWatchTime = Number.MAX_VALUE;

      // 提示用户可以答题
      uni.showToast({
        title: "视频播放完成，可以答题了",
        icon: "none",
        duration: 2000,
      });
    },

    // 监听用户开始拖拽进度条
    onSeeking (e) {
      const seekTime = e.detail.currentTime || 0;

      // 如果用户试图拖拽到超过最大观看时间的位置，阻止并回退
      if (seekTime > this.maxWatchTime + 1) {
        setTimeout(() => {
          const videoContext = uni.createVideoContext('myVideo', this);
          if (videoContext) {
            videoContext.seek(this.maxWatchTime);
          }
        }, 100);

        uni.showToast({
          title: "不允许快进，请完整观看",
          icon: "none",
          duration: 2000
        });
      }
    },

    // 监听用户完成拖拽进度条
    onSeeked (e) {
      const seekTime = e.detail.currentTime || 0;

      // 再次检查，确保用户没有快进
      if (seekTime > this.maxWatchTime + 1) {
        const videoContext = uni.createVideoContext('myVideo', this);
        if (videoContext) {
          videoContext.seek(this.maxWatchTime);
        }

        uni.showToast({
          title: "已回退到正确位置",
          icon: "none",
          duration: 1500
        });
      }
    },

    // 播放事件
    onPlay () {
      // 视频开始播放，记录播放开始时间
      this.watchStartTime = Date.now();

    },

    // 暂停事件
    onPause () {
      // 视频暂停，累加观看时长
      if (this.watchStartTime) {
        const now = Date.now();
        const watchDuration = (now - this.watchStartTime) / 1000;
        this.totalWatchTime += watchDuration;
        this.watchStartTime = null; // 清空开始时间，避免重复计算

      }
    },

    // 视频元数据加载完成
    onLoadedMetadata () {
      // 视频元数据加载完成
    },

    // 视频等待数据
    onWaiting () {
      // 视频等待数据
    },

    // 视频可以播放
    onCanPlay () {
      // 视频可以播放
    },

    // 全屏状态变化
    onFullscreenChange (e) {
      this.isFullscreen = e.detail.fullScreen;

      // 全屏时强制横屏
      if (this.isFullscreen) {
        this.lockOrientation()
      } else {
        this.unlockOrientation()
      }
    },

    // 强制横屏
    async lockOrientation () {
      try {
        // 方法1: 标准 Screen Orientation API
        if (screen.orientation && screen.orientation.lock) {
          await screen.orientation.lock('landscape')
          return
        }

        // 方法2: 旧版 screen.lockOrientation
        if (screen.lockOrientation) {
          screen.lockOrientation('landscape')
          return
        }

        // 方法3: webkit 前缀
        if (screen.webkitLockOrientation) {
          screen.webkitLockOrientation('landscape')
          return
        }

        // 方法4: moz 前缀
        if (screen.mozLockOrientation) {
          screen.mozLockOrientation('landscape')
          return
        }
      } catch (error) {
        // 横屏锁定失败，静默处理
      }
    },

    // 解锁屏幕方向
    unlockOrientation () {
      try {
        // 方法1: 标准 Screen Orientation API
        if (screen.orientation && screen.orientation.unlock) {
          screen.orientation.unlock()
          return
        }

        // 方法2: 旧版 screen.unlockOrientation
        if (screen.unlockOrientation) {
          screen.unlockOrientation()
          return
        }

        // 方法3: webkit 前缀
        if (screen.webkitUnlockOrientation) {
          screen.webkitUnlockOrientation()
          return
        }

        // 方法4: moz 前缀
        if (screen.mozUnlockOrientation) {
          screen.mozUnlockOrientation()
          return
        }
      } catch (error) {
        // 屏幕方向解锁失败，静默处理
      }
    },

    // ===== 问答相关 =====
    // 提交答案
    async onQuizSubmit (answerData) {
      try {
        if (!this.batchId) {
          console.warn('缺少批次ID，无法提交答案');
          return;
        }

        console.log('提交答案:', answerData);

        // 调用API提交答案
        const response = await this.submitAnswerWithoutAuth(this.getCurrentUserId(), {
          batchId: this.batchId,
          answers: answerData.answers,
          score: answerData.score,
          isCorrect: answerData.isCorrect,
          completedAt: new Date().toISOString()
        });

        if (response.success) {
          console.log('答案提交成功:', response.data);
          uni.showToast({
            title: '答案提交成功',
            icon: 'success'
          });
        } else {
          console.error('答案提交失败:', response.msg);
          uni.showToast({
            title: '答案提交失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('提交答案失败:', error);
        uni.showToast({
          title: '提交失败，请重试',
          icon: 'none'
        });
      }
    },

    // 答题完成
    async onQuizComplete (rewardData) {
      try {
        console.log('答题完成，奖励信息:', rewardData);

        // 如果有奖励，调用API发放奖励
        if (rewardData && rewardData.rewardAmount > 0) {
          const response = await this.grantRewardWithoutAuth(this.getCurrentUserId(), {
            batchId: this.batchId,
            rewardAmount: rewardData.rewardAmount,
            rewardType: 'quiz_completion',
            description: '完成视频答题奖励'
          });

          if (response.success) {
            console.log('奖励发放成功:', response.data);
            uni.showToast({
              title: `恭喜获得 ${rewardData.rewardAmount} 元奖励！`,
              icon: 'success',
              duration: 3000
            });
          } else {
            console.error('奖励发放失败:', response.msg);
          }
        } else {
          // 没有奖励时也显示完成提示
          uni.showToast({
            title: '答题完成！',
            icon: 'success',
            duration: 2000
          });
        }

        // 移除自动返回上一页的逻辑，让用户留在当前页面
        console.log('答题完成，用户可以继续观看视频或手动返回');
      } catch (error) {
        console.error('处理答题完成失败:', error);
        uni.showToast({
          title: '答题完成处理失败',
          icon: 'none',
          duration: 2000
        });
      }
    },

    // ===== 进度监控相关 =====
    // 开始进度监控
    startProgressMonitoring () {
      if (this.progressTimer) {
        clearInterval(this.progressTimer);
      }

      this.watchStartTime = Date.now();

      // 检查是否需要创建观看记录
      const existingRecord = this.getLocalWatchRecord();
      if (!this.recordCreated && !existingRecord) {
        this.createWatchRecord();
      } else if (existingRecord) {
        // 使用已存在的记录
        this.recordCreated = true;
        this.viewRecordId = existingRecord.id;
      }

      // 每5秒输出一次观看进度
      this.progressTimer = setInterval(() => {
        this.outputWatchProgress();
      }, 5000);
    },

    // 停止进度监控
    stopProgressMonitoring () {
      // 在停止监控前，先处理最后一次观看时长累加
      if (this.watchStartTime) {
        const now = Date.now();
        const watchDuration = (now - this.watchStartTime) / 1000;
        this.totalWatchTime += watchDuration;
        this.watchStartTime = null;

      }

      if (this.progressTimer) {
        clearInterval(this.progressTimer);
        this.progressTimer = null;
      }

      // 最后提交一次观看进度
      this.submitWatchProgress();
    },

    // 输出观看进度
    outputWatchProgress () {
      // 只有在播放状态下才累加观看时长
      if (this.watchStartTime) {
        const now = Date.now();
        const watchDuration = (now - this.watchStartTime) / 1000;
        this.totalWatchTime += watchDuration;
        this.watchStartTime = now; // 重置开始时间为当前时间

      }

      // 调用API将进度数据发送到服务器
      this.submitWatchProgress();
    },

    // 创建观看记录
    async createWatchRecord () {
      if (!this.batchId) {
        return;
      }

      // 防止并发调用
      if (this.isCreatingRecord) {
        return;
      }

      // 检查本地存储中是否已有记录
      const existingRecord = this.getLocalWatchRecord();
      if (existingRecord && existingRecord.batchId === this.batchId) {
        this.recordCreated = true;
        this.viewRecordId = existingRecord.id;
        return;
      }

      // 检查内存中是否已创建
      if (this.recordCreated && this.viewRecordId) {
        return;
      }

      this.isCreatingRecord = true;

      try {
        // 构建推广链接信息
        let promotionLink = '';
        if (this.sharerId) {
          promotionLink = `shared_by_${this.sharerId}`;
        }

        // 优先使用新的统一API
        const response = await this.createOrGetRecordWithoutAuth({
          batchId: this.batchId,
          userId: this.getCurrentUserId(),
          promotionLink: promotionLink
        });

        if (response.success && response.data) {
          this.viewRecordId = response.data.id;
          this.recordCreated = true;

          // 保存到本地存储
          this.saveLocalWatchRecord(response.data);

          // 开始观看
          await this.startWatchingWithoutAuth(this.getCurrentUserId(), this.batchId);
          return;
        }
      } catch (error) {
        // 静默处理错误，避免重复日志
      }

      // 回退到旧API
      try {
        const response = await createViewRecord({
          batchId: this.batchId,
          userId: this.getCurrentUserId(),
          promotionLink: ''
        });

        if (response.success && response.data) {
          this.viewRecordId = response.data;
          this.recordCreated = true;

          // 保存到本地存储
          this.saveLocalWatchRecord({ id: response.data });
        }
      } catch (error) {
        // 静默处理错误
      } finally {
        this.isCreatingRecord = false;
      }
    },

    // 提交观看进度到服务器
    async submitWatchProgress () {
      if (!this.batchId) {
        console.warn('缺少批次ID，无法更新观看进度');
        return;
      }

      try {
        // 改进观看进度计算，避免除零错误和不准确的计算
        let watchProgress = 0;
        if (this.duration > 0 && this.maxWatchTime >= 0) {
          watchProgress = Math.min(this.maxWatchTime / this.duration, 1.0); // 确保不超过100%
        }


        // 优先使用新的统一API
        const response = await this.updateWatchProgressWithoutAuth(this.getCurrentUserId(), {
          batchId: this.batchId,
          viewDuration: Math.floor(this.totalWatchTime),
          watchProgress: watchProgress,
          isCompleted: this.videoCompleted
        });

        if (response.success) {

          return;
        }
      } catch (error) {
        console.error('新API更新观看进度失败，尝试旧API:', error);
      }

      // 回退到旧API
      try {
        // 改进观看进度计算，避免除零错误和不准确的计算
        let watchProgress = 0;
        if (this.duration > 0 && this.maxWatchTime >= 0) {
          watchProgress = Math.min(this.maxWatchTime / this.duration, 1.0); // 确保不超过100%
        }

        const response = await updateViewProgress({
          batchId: this.batchId,
          viewDuration: Math.floor(this.totalWatchTime),
          watchProgress: watchProgress,
          isCompleted: this.videoCompleted
        });

        if (!response.success) {
          console.error('更新观看进度失败:', response.msg);
        }
      } catch (error) {
        console.error('提交观看进度失败:', error);
      }
    },

    // 获取当前用户ID
    getCurrentUserId () {
      // 如果已选择用户，返回选中的用户ID
      if (this.selectedUserId) {
        return this.selectedUserId;
      }

      // 如果没有选择用户，随机选择一个
      if (this.availableUsers.length > 0) {
        const randomIndex = Math.floor(Math.random() * this.availableUsers.length);
        this.selectedUserId = this.availableUsers[randomIndex].id;
        console.log('随机选择用户:', this.selectedUserId, this.availableUsers[randomIndex].nickname);
        return this.selectedUserId;
      }

      // 兜底返回默认用户
      return '1';
    },

    // 显示用户选择器
    showUserSelectorModal () {
      this.showUserSelector = true;
    },

    // 关闭用户选择器
    closeUserSelector () {
      this.showUserSelector = false;
    },

    // 选择用户
    selectUser (user) {
      this.selectedUserId = user.id;
      this.showUserSelector = false;
      console.log('选择用户:', user.nickname, 'ID:', user.id);

      uni.showToast({
        title: `已选择: ${user.nickname}`,
        icon: 'success'
      });

      // 开始加载视频数据
      this.loadVideoData();

      // 开始进度监控
      this.startProgressMonitoring();
    },

    // 随机选择用户
    selectRandomUser () {
      if (this.availableUsers.length > 0) {
        const randomIndex = Math.floor(Math.random() * this.availableUsers.length);
        const randomUser = this.availableUsers[randomIndex];
        this.selectUser(randomUser);
      }
    },

    // 初始化模拟微信用户
    initMockWechatUser () {
      // 使用微信用户服务初始化模拟用户
      const result = wechatUserService.initMockWechatUser();
      console.log('模拟微信用户初始化完成:', result.userInfo.nickname);
    },

    // 解析URL参数
    parseUrlParams () {
      try {
        const url = window.location.href;
        const hash = url.split('#')[1];
        if (hash && hash.includes('?')) {
          const queryString = hash.split('?')[1];
          const params = new URLSearchParams(queryString);
          const options = {};

          for (let [key, value] of params) {
            options[key] = value;
          }

          // 更新实例属性
          if (options.videoId) {
            this.videoId = parseInt(options.videoId);
          }
          if (options.batchId) {
            this.batchId = parseInt(options.batchId);
          }
          if (options.sharerId) {
            this.sharerId = options.sharerId;
          }

          return options;
        }
        return null;
      } catch (error) {
        console.error('解析URL参数失败:', error);
        return null;
      }
    },

    // 记录分享人信息
    recordSharerInfo () {
      try {
        console.log('记录分享人信息:', this.sharerId);
        // 这里可以调用API记录分享信息，或者在创建观看记录时包含分享人信息
        // 暂时先记录到本地存储
        uni.setStorageSync('currentSharerInfo', {
          sharerId: this.sharerId,
          batchId: this.batchId,
          videoId: this.videoId,
          shareTime: new Date().toISOString()
        });
      } catch (error) {
        console.error('记录分享人信息失败:', error);
      }
    }
  }
}
</script>

<style>
/* 主容器 */
.container {
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  min-height: calc(100vh - var(--window-bottom));
  width: 100%;
}

/* 视频播放器样式 */
.video-player {
  width: 100%;
  height: 422rpx;
  background: #000;
  border: 2rpx solid #39BFFD;
  border-radius: 8rpx;
}

/* 内容区域 */
.video-content {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
}

/* 视频信息 */
.video-info {
  background: white;
  padding: 20rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.video-info .video-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.video-meta {
  display: flex;
  justify-content: space-between;
  font-size: 26rpx;
  color: #999;
  margin-bottom: 16rpx;
}

.description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 用户选择器样式 */
.user-selector-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.user-selector-content {
  background: #fff;
  border-radius: 20rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.selector-header {
  padding: 40rpx 30rpx 20rpx;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}

.selector-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.selector-subtitle {
  font-size: 28rpx;
  color: #666;
  display: block;
}

.user-list {
  flex: 1;
  padding: 20rpx 0;
  max-height: 500rpx;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1px solid #f5f5f5;
  transition: background-color 0.2s;
}

.user-item:hover {
  background-color: #f8f8f8;
}

.user-item:last-child {
  border-bottom: none;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  background: #f0f0f0;
}

.user-info {
  flex: 1;
}

.user-nickname {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 5rpx;
}

.user-id {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.select-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.select-icon .icon {
  font-size: 28rpx;
  color: #007aff;
  font-weight: bold;
}

.selector-footer {
  padding: 20rpx 30rpx 40rpx;
  border-top: 1px solid #f0f0f0;
}

.random-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(45deg, #007aff, #5ac8fa);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.random-btn:active {
  opacity: 0.8;
}
</style>
