/**
 * 统一存储基类
 * 抽象通用的存储操作模式，为认证服务提供可复用的存储基础功能
 */

/**
 * 存储基类
 * 提供通用的存储操作方法
 */
export class StorageBase {
  /**
   * 构造函数
   * @param {Object} storageKeys - 存储键名配置
   */
  constructor(storageKeys) {
    this.storageKeys = storageKeys
  }

  /**
   * 保存数据到存储
   * @param {string} key - 存储键名
   * @param {any} data - 要保存的数据
   */
  saveData(key, data) {
    try {
      uni.setStorageSync(key, data)
    } catch (error) {
      console.error(`Error saving data to ${key}:`, error)
    }
  }

  /**
   * 从存储获取数据
   * @param {string} key - 存储键名
   * @returns {any} 存储的数据，如果不存在返回null
   */
  getData(key) {
    try {
      return uni.getStorageSync(key) || null
    } catch (error) {
      console.error(`Error getting data from ${key}:`, error)
      return null
    }
  }

  /**
   * 从存储删除数据
   * @param {string} key - 存储键名
   */
  removeData(key) {
    try {
      uni.removeStorageSync(key)
    } catch (error) {
      console.error(`Error removing data from ${key}:`, error)
    }
  }

  /**
   * 清空所有相关存储数据
   * @param {Array} keys - 要清空的存储键名数组
   */
  clearData(keys) {
    keys.forEach(key => {
      this.removeData(key)
    })
  }
}

/**
 * 用户认证存储基类
 * 为用户认证服务提供通用的存储操作
 */
export class AuthStorageBase extends StorageBase {
  /**
   * 保存用户信息
   * @param {Object} userInfo - 用户信息
   */
  saveUserInfo(userInfo) {
    this.saveData(this.storageKeys.USER_INFO, userInfo)
  }

  /**
   * 获取用户信息
   * @returns {Object|null} 用户信息
   */
  getUserInfo() {
    return this.getData(this.storageKeys.USER_INFO)
  }

  /**
   * 保存用户令牌
   * @param {string} token - 用户令牌
   */
  saveUserToken(token) {
    if (this.storageKeys.USER_TOKEN) {
      this.saveData(this.storageKeys.USER_TOKEN, token)
    }
  }

  /**
   * 获取用户令牌
   * @returns {string|null} 用户令牌
   */
  getUserToken() {
    if (this.storageKeys.USER_TOKEN) {
      return this.getData(this.storageKeys.USER_TOKEN)
    }
    return null
  }

  /**
   * 保存用户权限
   * @param {Array} permissions - 用户权限列表
   */
  saveUserPermissions(permissions) {
    if (this.storageKeys.USER_PERMISSIONS) {
      this.saveData(this.storageKeys.USER_PERMISSIONS, permissions)
    }
  }

  /**
   * 获取用户权限
   * @returns {Array} 用户权限列表
   */
  getUserPermissions() {
    if (this.storageKeys.USER_PERMISSIONS) {
      return this.getData(this.storageKeys.USER_PERMISSIONS) || []
    }
    return []
  }

  /**
   * 检查用户是否已登录
   * @returns {boolean} 是否已登录
   */
  isLoggedIn() {
    const userInfo = this.getUserInfo()
    return !!(userInfo && (userInfo.username || userInfo.id || userInfo.openId))
  }

  /**
   * 获取用户ID
   * @returns {string|null} 用户ID
   */
  getUserId() {
    const userInfo = this.getUserInfo()
    return userInfo ? (userInfo.userId || userInfo.id) : null
  }

  /**
   * 获取用户名
   * @returns {string|null} 用户名
   */
  getUsername() {
    const userInfo = this.getUserInfo()
    return userInfo ? (userInfo.username || userInfo.nickname) : null
  }

  /**
   * 获取用户显示名称
   * @returns {string|null} 用户显示名称
   */
  getUserDisplayName() {
    const userInfo = this.getUserInfo()
    if (!userInfo) return null
    
    return userInfo.nickName || userInfo.nickname || userInfo.username || userInfo.name || null
  }

  /**
   * 获取用户头像
   * @returns {string|null} 用户头像URL
   */
  getUserAvatar() {
    const userInfo = this.getUserInfo()
    return userInfo ? userInfo.avatar : null
  }

  /**
   * 检查用户是否有指定权限
   * @param {string} permission - 权限名称
   * @returns {boolean} 是否有权限
   */
  hasPermission(permission) {
    const permissions = this.getUserPermissions()
    return permissions.includes('*') || permissions.includes(permission)
  }

  /**
   * 用户登出（清理存储数据）
   * @returns {boolean} 是否成功
   */
  logout() {
    try {
      const keysToRemove = Object.values(this.storageKeys)
      this.clearData(keysToRemove)
      return true
    } catch (error) {
      console.error('Error during logout:', error)
      return false
    }
  }
}

/**
 * 会话管理混入
 * 为认证服务提供会话管理功能
 */
export class SessionMixin {
  /**
   * 检查登录会话是否有效
   * @param {number} sessionDuration - 会话持续时间（毫秒），默认7天
   * @returns {boolean} 会话是否有效
   */
  isSessionValid(sessionDuration = 7 * 24 * 60 * 60 * 1000) {
    const userInfo = this.getUserInfo()
    if (!userInfo || !userInfo.loginTime) {
      return false
    }

    const currentTime = new Date().getTime()
    return (currentTime - userInfo.loginTime) < sessionDuration
  }

  /**
   * 刷新登录会话
   */
  refreshSession() {
    const userInfo = this.getUserInfo()
    if (userInfo) {
      userInfo.loginTime = new Date().getTime()
      this.saveUserInfo(userInfo)
    }
  }

  /**
   * 设置登录时间
   * @param {Object} userInfo - 用户信息对象
   * @returns {Object} 更新后的用户信息
   */
  setLoginTime(userInfo) {
    return {
      ...userInfo,
      loginTime: new Date().getTime()
    }
  }
}

/**
 * 导航管理混入
 * 为认证服务提供页面导航功能
 */
export class NavigationMixin {
  /**
   * 跳转到登录页面
   * @param {string} loginPath - 登录页面路径
   */
  redirectToLogin(loginPath = '/pages/login/index') {
    uni.reLaunch({
      url: loginPath
    })
  }

  /**
   * 跳转到主页面
   * @param {string} homePath - 主页面路径
   */
  redirectToHome(homePath = '/pages/index/index') {
    uni.reLaunch({
      url: homePath
    })
  }

  /**
   * 显示TabBar
   */
  showTabBar() {
    try {
      uni.showTabBar({
        success: () => {
          console.log('TabBar显示成功')
        },
        fail: (err) => {
          console.log('TabBar显示失败:', err)
        }
      })
    } catch (e) {
      console.log('TabBar操作异常:', e)
    }
  }

  /**
   * 隐藏TabBar
   */
  hideTabBar() {
    try {
      uni.hideTabBar()
    } catch (e) {
      console.log('TabBar隐藏异常:', e)
    }
  }
}

/**
 * 创建认证服务的工厂函数
 * @param {Object} config - 配置对象
 * @param {Object} config.storageKeys - 存储键名配置
 * @param {Object} config.apiService - API服务对象
 * @param {string} config.userType - 用户类型
 * @returns {Class} 认证服务类
 */
export function createAuthService(config) {
  const { storageKeys, apiService, userType } = config

  return class AuthService extends AuthStorageBase {
    constructor() {
      super(storageKeys)
      this.apiService = apiService
      this.userType = userType
    }

    // 混入会话管理功能
    isSessionValid = SessionMixin.prototype.isSessionValid.bind(this)
    refreshSession = SessionMixin.prototype.refreshSession.bind(this)
    setLoginTime = SessionMixin.prototype.setLoginTime.bind(this)

    // 混入导航管理功能
    redirectToLogin = NavigationMixin.prototype.redirectToLogin.bind(this)
    redirectToHome = NavigationMixin.prototype.redirectToHome.bind(this)
    showTabBar = NavigationMixin.prototype.showTabBar.bind(this)
    hideTabBar = NavigationMixin.prototype.hideTabBar.bind(this)
  }
}

// 默认导出
export default {
  StorageBase,
  AuthStorageBase,
  SessionMixin,
  NavigationMixin,
  createAuthService
}
