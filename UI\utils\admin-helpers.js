/**
 * Admin 页面通用工具函数
 * 简单实用的工具函数，避免重复代码
 */

/**
 * 安全解析 ID
 * @param {string|number} value - 要解析的值
 * @returns {number|null} 解析后的 ID 或 null
 */
export const safeParseId = (value) => {
  if (!value) return null
  const id = parseInt(value)
  return isNaN(id) ? null : id
}

/**
 * 显示错误并返回上一页
 * @param {string} message - 错误消息
 * @param {number} delay - 延迟时间（毫秒）
 */
export const showErrorAndGoBack = (message = '参数错误', delay = 1500) => {
  uni.showToast({
    title: message,
    icon: 'none',
    duration: 2000
  })
  setTimeout(() => {
    uni.navigateBack()
  }, delay)
}

/**
 * API 调用包装器（自动处理 loading 和错误）
 * @param {Function} apiCall - API 调用函数
 * @param {string} loadingTitle - 加载提示文字
 * @param {string} errorTitle - 错误提示文字
 * @returns {Promise} API 调用结果
 */
export const apiWrapper = async (apiCall, loadingTitle = '加载中...', errorTitle = '操作失败') => {
  try {
    uni.showLoading({ title: loadingTitle })
    const result = await apiCall()
    uni.hideLoading()
    return result
  } catch (error) {
    uni.hideLoading()
    uni.showToast({
      title: errorTitle,
      icon: 'none'
    })
    throw error
  }
}

/**
 * 验证页面参数
 * @param {Object} options - 页面参数
 * @param {Array} requiredParams - 必需参数列表
 * @returns {Object} 验证结果 { valid: boolean, params: Object }
 */
export const validatePageParams = (options = {}, requiredParams = []) => {
  const result = { valid: true, params: {} }
  
  for (const param of requiredParams) {
    if (!options[param]) {
      result.valid = false
      break
    }
    result.params[param] = options[param]
  }
  
  return result
}

/**
 * 格式化用户状态
 * @param {number} status - 状态值
 * @returns {string} 状态文字
 */
export const formatUserStatus = (status) => {
  const statusMap = {
    0: '禁用',
    1: '正常',
    2: '待审核'
  }
  return statusMap[status] ?? '未知'
}

/**
 * 格式化批次状态
 * @param {string} status - 状态值
 * @returns {string} 状态文字
 */
export const formatBatchStatus = (status) => {
  const statusMap = {
    'active': '进行中',
    'completed': '已完成',
    'pending': '待开始',
    'cancelled': '已取消'
  }
  return statusMap[status] ?? '未知'
}

/**
 * 安全获取嵌套对象属性
 * @param {Object} obj - 对象
 * @param {string} path - 属性路径，如 'user.profile.name'
 * @param {any} defaultValue - 默认值
 * @returns {any} 属性值
 */
export const safeGet = (obj, path, defaultValue = null) => {
  return path.split('.').reduce((current, key) => current?.[key], obj) ?? defaultValue
}

/**
 * 确认对话框
 * @param {string} message - 确认消息
 * @param {string} title - 标题
 * @returns {Promise<boolean>} 用户选择结果
 */
export const showConfirm = (message, title = '提示') => {
  return new Promise((resolve) => {
    uni.showModal({
      title,
      content: message,
      success: (res) => {
        resolve(res.confirm)
      },
      fail: () => {
        resolve(false)
      }
    })
  })
}

/**
 * 切换用户状态的通用方法
 * @param {Object} user - 用户对象
 * @param {Function} toggleApi - 切换状态的 API 函数
 * @param {Function} onSuccess - 成功回调
 * @returns {Promise<boolean>} 操作结果
 */
export const toggleUserStatus = async (user, toggleApi, onSuccess = null) => {
  const action = user.status === 1 ? '禁用' : '启用'
  const confirmed = await showConfirm(`确定要${action}该用户吗？`)
  
  if (!confirmed) return false
  
  try {
    await apiWrapper(
      () => toggleApi(user.id),
      `${action}中...`,
      `${action}失败`
    )
    
    // 更新本地状态
    user.status = user.status === 1 ? 0 : 1
    
    uni.showToast({
      title: `${action}成功`,
      icon: 'success'
    })
    
    if (onSuccess) onSuccess()
    return true
  } catch (error) {
    return false
  }
}

/**
 * 通用的页面初始化方法
 * @param {Object} options - 页面参数
 * @param {Array} requiredParams - 必需参数
 * @param {Function} loadDataFn - 数据加载函数
 * @returns {Promise<boolean>} 初始化结果
 */
export const initPage = async (options, requiredParams = [], loadDataFn = null) => {
  // 验证参数
  const validation = validatePageParams(options, requiredParams)
  if (!validation.valid) {
    showErrorAndGoBack('参数错误')
    return false
  }
  
  // 加载数据
  if (loadDataFn) {
    try {
      await loadDataFn(validation.params)
    } catch (error) {
      console.error('页面数据加载失败:', error)
      return false
    }
  }
  
  return true
}
