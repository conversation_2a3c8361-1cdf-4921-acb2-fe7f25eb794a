/**
 * 统一配置管理工具
 * 合并原 config.js 和 app-config.js 的功能，提供统一的配置获取接口
 */

// 环境类型
export const ENV_TYPES = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production',
  TEST: 'test'
}

// 当前环境（可以通过构建工具或其他方式动态设置）
export const CURRENT_ENV = ENV_TYPES.DEVELOPMENT

/**
 * 统一配置管理器
 */
export class ConfigManager {
  /**
   * 获取外部配置项
   * @param {string} key 配置项键名，支持点号分隔的嵌套键名
   * @param {any} defaultValue 默认值
   * @returns {any} 配置项值
   */
  static getConfig(key, defaultValue = null) {
    if (!window.APP_CONFIG) {
      return defaultValue
    }

    // 支持嵌套键名，如 'storage.expireTime'
    const keys = key.split('.')
    let value = window.APP_CONFIG

    for (const k of keys) {
      if (value?.[k] !== undefined) {
        value = value[k]
      } else {
        return defaultValue
      }
    }

    return value
  }

  /**
   * 设置应用配置项
   * @param {string} key 配置项键名
   * @param {any} value 配置项值
   */
  static setConfig(key, value) {
    if (window.setAppConfig) {
      window.setAppConfig(key, value)
    }
  }

  /**
   * 检查配置是否已加载
   * @returns {boolean} 配置是否已加载
   */
  static isConfigLoaded() {
    return !!(window.APP_CONFIG)
  }

  /**
   * 等待配置加载完成
   * @param {number} timeout 超时时间（毫秒）
   * @returns {Promise<boolean>} 是否加载成功
   */
  static async waitForConfig(timeout = 5000) {
    return new Promise((resolve) => {
      if (this.isConfigLoaded()) {
        resolve(true)
        return
      }

      let attempts = 0
      const maxAttempts = timeout / 100

      const checkConfig = () => {
        attempts++
        if (this.isConfigLoaded()) {
          resolve(true)
        } else if (attempts >= maxAttempts) {
          resolve(false)
        } else {
          setTimeout(checkConfig, 100)
        }
      }

      checkConfig()
    })
  }

  /**
   * 获取API基础地址
   * @returns {string} API基础地址
   */
  static getApiBaseUrl() {
    return this.getConfig('apiBaseUrl', 'https://localhost:7048/api')
  }

  /**
   * 获取UI项目访问地址
   * @returns {string} UI项目的访问地址
   */
  static getUIProjectUrl() {
    // 检查全局配置是否存在
    if (!window.APP_CONFIG) {
      throw new Error('配置文件未加载！请检查 /static/config/app-config.js 文件是否存在且正确加载。')
    }

    // 检查UIProjectUrl配置是否存在
    if (!window.APP_CONFIG.UIProjectUrl) {
      throw new Error('UIProjectUrl 配置项未找到！请检查 app-config.js 文件中的 UIProjectUrl 配置。')
    }

    return window.APP_CONFIG.UIProjectUrl
  }

  /**
   * 获取上传配置
   * @returns {Object} 上传配置对象
   */
  static getUploadConfig() {
    return this.getConfig('upload', {
      maxFileSize: 2 * 1024 * 1024 * 1024,
      allowedVideoFormats: ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'],
      allowedImageFormats: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
    })
  }

  /**
   * 获取压缩配置
   * @returns {Object} 压缩配置对象
   */
  static getCompressionConfig() {
    return this.getConfig('compression', {
      defaultQuality: 7,
      qualityRange: [1, 10],
      defaultEnabled: true
    })
  }

  /**
   * 获取功能开关配置
   * @returns {Object} 功能开关配置对象
   */
  static getFeaturesConfig() {
    return this.getConfig('features', {
      enableCache: true,
      enableOffline: false,
      enableAnalytics: false
    })
  }

  /**
   * 获取UI配置
   * @returns {Object} UI配置对象
   */
  static getUIConfig() {
    return this.getConfig('ui', {
      colors: {
        primary: '#186BFF',
        success: '#52C41A',
        warning: '#FAAD14',
        error: '#F5222D',
        info: '#186BFF'
      },
      pageSize: 20,
      maxUploadSize: 2 * 1024 * 1024 * 1024,
      animationDuration: 300
    })
  }
}

// API配置 - 使用统一的配置管理器
export const API_CONFIG = {
  // 获取API基础URL
  get BASE_URL() {
    return ConfigManager.getApiBaseUrl()
  },

  // 请求配置
  get TIMEOUT() {
    return ConfigManager.getConfig('apiTimeout', 30000)
  },
  get RETRY_COUNT() {
    return ConfigManager.getConfig('apiRetryCount', 3)
  },
  get RETRY_DELAY() {
    return ConfigManager.getConfig('apiRetryDelay', 1000)
  },

  // 默认请求头
  get DEFAULT_HEADERS() {
    return ConfigManager.getConfig('apiDefaultHeaders', {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    })
  }
}

// 存储配置
export const STORAGE_CONFIG = {
  // 存储键名
  KEYS: {
    TOKEN: 'auth_token',
    USER_INFO: 'user_info',
    LOGIN_INFO: 'loginInfo',
    ADMIN_LOGIN_INFO: 'adminLoginInfo',
    USER_PERMISSIONS: 'userPermissions',
    APP_CONFIG: 'app_config'
  },

  // 存储过期时间（毫秒）- 从外部配置文件读取
  get EXPIRE_TIME() {
    const expireTime = ConfigManager.getConfig('storage.expireTime', {
      token: 7 * 24 * 60 * 60 * 1000, // 7天
      userInfo: 24 * 60 * 60 * 1000,  // 1天
      cache: 30 * 60 * 1000            // 30分钟
    })

    // 转换为大写键名以保持向后兼容
    return {
      TOKEN: expireTime.token,
      USER_INFO: expireTime.userInfo,
      CACHE: expireTime.cache
    }
  }
}

// 页面路由配置
export const ROUTE_CONFIG = {
  LOGIN: '/pages/login/index',
  HOME: '/pages/index/index',
  ADMIN: '/pages/admin/index',
  USER: '/pages/user/index',
  VIDEO: '/pages/video/index'
}

// 应用配置 - 使用统一的配置管理器
export const APP_CONFIG = {
  // 应用信息
  get APP_NAME() {
    return ConfigManager.getConfig('appName', '视频学习测验系统')
  },
  get VERSION() {
    return ConfigManager.getConfig('version', '1.0.0')
  },

  // 功能开关
  get FEATURES() {
    const features = ConfigManager.getFeaturesConfig()
    
    // 转换为大写键名以保持向后兼容
    return {
      ENABLE_CACHE: features.enableCache,
      ENABLE_OFFLINE: features.enableOffline,
      ENABLE_ANALYTICS: features.enableAnalytics
    }
  },

  // UI配置
  get UI() {
    const ui = ConfigManager.getUIConfig()
    
    // 转换为大写键名以保持向后兼容
    return {
      COLORS: {
        PRIMARY: ui.colors.primary,
        SUCCESS: ui.colors.success,
        WARNING: ui.colors.warning,
        ERROR: ui.colors.error,
        INFO: ui.colors.info
      },
      PAGE_SIZE: ui.pageSize,
      MAX_UPLOAD_SIZE: ui.maxUploadSize,
      ANIMATION_DURATION: ui.animationDuration
    }
  }
}

/**
 * 获取当前环境的API基础URL
 * @returns {string} API基础URL
 */
export const getApiBaseURL = () => API_CONFIG.BASE_URL

/**
 * 获取完整的API URL
 * @param {string} endpoint API端点
 * @returns {string} 完整的API URL
 */
export const getApiURL = (endpoint) => {
  const baseURL = getApiBaseURL()
  // 确保endpoint以/开头
  const normalizedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`
  return `${baseURL}${normalizedEndpoint}`
}

/**
 * 检查功能是否启用
 * @param {string} feature 功能名称
 * @returns {boolean} 是否启用
 */
export const isFeatureEnabled = (feature) => APP_CONFIG.FEATURES[feature] || false

/**
 * 获取存储键名
 * @param {string} key 键名
 * @returns {string} 完整的存储键名
 */
export const getStorageKey = (key) => STORAGE_CONFIG.KEYS[key] || key

/**
 * 检查是否为开发环境
 * @returns {boolean} 是否为开发环境
 */
export const isDevelopment = () => CURRENT_ENV === ENV_TYPES.DEVELOPMENT

/**
 * 检查是否为生产环境
 * @returns {boolean} 是否为生产环境
 */
export const isProduction = () => CURRENT_ENV === ENV_TYPES.PRODUCTION

// 便捷导出函数（保持向后兼容）
export const getAppConfig = ConfigManager.getConfig
export const setAppConfig = ConfigManager.setConfig
export const isConfigLoaded = ConfigManager.isConfigLoaded
export const waitForConfig = ConfigManager.waitForConfig
export const getUIProjectUrl = ConfigManager.getUIProjectUrl
export const getApiBaseUrl = ConfigManager.getApiBaseUrl
export const getUploadConfig = ConfigManager.getUploadConfig
export const getCompressionConfig = ConfigManager.getCompressionConfig
export const getFeaturesConfig = ConfigManager.getFeaturesConfig
export const getUIConfig = ConfigManager.getUIConfig

// 默认导出配置对象
export default {
  ConfigManager,
  ENV_TYPES,
  CURRENT_ENV,
  API_CONFIG,
  STORAGE_CONFIG,
  ROUTE_CONFIG,
  APP_CONFIG,
  
  // 工具函数
  getApiBaseURL,
  getApiURL,
  isFeatureEnabled,
  getStorageKey,
  isDevelopment,
  isProduction,
  
  // 便捷函数
  getAppConfig,
  setAppConfig,
  isConfigLoaded,
  waitForConfig,
  getUIProjectUrl,
  getApiBaseUrl,
  getUploadConfig,
  getCompressionConfig,
  getFeaturesConfig,
  getUIConfig
}
