# 员工权限配置总结

## 🎯 员工权限需求

根据业务需求，员工应该具备以下权限：

### ✅ 员工可以访问的功能

1. **用户管理** - 管理自己负责的用户
2. **用户审核** - 审核用户信息
3. **批次查看** - 查看批次信息，生成分享链接
4. **数据统计** - 查看仪表板数据
5. **个人设置** - 修改密码等

### ❌ 员工不能访问的功能

1. **视频管理** - 不能上传、编辑、删除视频
2. **批次创建** - 不能创建新批次（只能查看和生成链接）
3. **员工管理** - 不能管理其他员工
4. **管理员管理** - 不能管理管理员

## 🔧 权限配置修改

### 1. 页面访问权限 (PAGE_PERMISSIONS)

```javascript
// 员工可以访问
'/pages/index/index': ['employee', 'manager', 'agent', 'admin', 'super_admin'],
'/pages/admin/users/user-management': ['employee', 'manager', 'agent', 'admin', 'super_admin'],
'/pages/admin/media/index': ['employee', 'manager', 'agent', 'admin', 'super_admin'], // 员工可以查看批次
'/pages/user/index': ['employee', 'manager', 'agent', 'admin', 'super_admin'],

// 员工不能访问
'/pages/video/index': ['user', 'wechat_user', 'manager', 'agent', 'admin', 'super_admin'], // 移除了 employee
```

### 2. 功能权限 (FEATURE_PERMISSIONS)

```javascript
// 员工可以使用的功能
'view_dashboard': ['employee', 'manager', 'agent', 'admin', 'super_admin'],
'manage_users': ['employee', 'manager', 'agent', 'admin', 'super_admin'], // 新增 employee
'user_audit': ['employee', 'manager', 'agent', 'admin', 'super_admin'], // 新增 employee
'create_user': ['employee', 'manager', 'agent', 'admin', 'super_admin'], // 新增 employee
'edit_user': ['employee', 'manager', 'agent', 'admin', 'super_admin'], // 新增 employee
'disable_user': ['employee', 'manager', 'agent', 'admin', 'super_admin'], // 新增 employee

// 员工不能使用的功能
'manage_videos': ['manager', 'agent', 'admin', 'super_admin'], // 不包含 employee
'create_batch': ['manager', 'agent', 'admin', 'super_admin'], // 员工不能创建批次
'delete_user': ['admin', 'super_admin'], // 只有管理员以上可以删除

// 员工可以使用的批次功能
'view_batch': ['employee', 'manager', 'agent', 'admin', 'super_admin'], // 员工可以查看批次
'manage_batch': ['employee', 'manager', 'agent', 'admin', 'super_admin'], // 员工可以管理批次（生成链接等）
```

### 3. 页面级权限检查

在 `/pages/admin/media/index.vue` 中添加了页面级权限检查：

```javascript
onLoad() {
    // 检查页面访问权限
    if (!this.canAccessPage('/pages/admin/media/index')) {
        uni.showModal({
            title: '权限不足',
            content: '您没有权限访问此页面',
            showCancel: false,
            success: () => {
                uni.switchTab({
                    url: '/pages/index/index'
                });
            }
        });
        return;
    }
}
```

## 📋 权限验证清单

### 员工登录后应该能看到：

- ✅ 数据统计页面（首页）
- ✅ 我的页面
- ✅ "数据统计" 菜单项
- ✅ "用户信息" 菜单项
- ✅ "用户审核" 菜单项
- ✅ "修改密码" 菜单项

### 员工登录后不应该看到：

- ❌ 内容库页面（底部导航栏会隐藏或跳转）
- ❌ 视频管理相关功能
- ❌ 批次管理相关功能
- ❌ "管理信息" 菜单项
- ❌ "员工信息" 菜单项

### 员工在用户管理页面应该能：

- ✅ 查看用户列表
- ✅ 搜索用户
- ✅ 查看用户详情
- ✅ 创建新用户
- ✅ 编辑用户信息
- ✅ 禁用/启用用户
- ✅ 审核用户

### 员工在用户管理页面不应该能：

- ❌ 删除用户（只有管理员以上可以）

## 🔍 测试方法

1. **使用员工账号登录**
2. **检查底部导航栏** - 点击"内容库"应该提示权限不足
3. **检查"我的"页面菜单** - 应该能看到用户相关菜单，看不到管理员菜单
4. **访问用户管理页面** - 应该能正常访问和操作
5. **尝试访问视频管理** - 应该被拒绝访问

## 🔧 UI 控制优化

### 1. 底部导航栏权限控制

由于 uni-app 的 tabBar 是静态配置，无法动态隐藏，采用了页面级权限检查：

```javascript
// 在 /pages/admin/media/index.vue 中
onLoad() {
    if (!this.canAccessPage('/pages/admin/media/index')) {
        // 员工访问时直接跳转到首页
        uni.switchTab({ url: '/pages/index/index' });
        return;
    }
}
```

### 2. 删除批次页面的创建按钮

- ✅ 删除了 `BatchesList.vue` 中的"创建批次"按钮
- ✅ 删除了对应的 `createNewBatch()` 方法
- ✅ 现在批次页面只能查看，不能创建

### 3. 视频详情页面的权限控制

在视频详情页面中，操作按钮根据权限动态显示：

```javascript
// 动态生成操作列表
actionList() {
    const actions = [];

    if (this.canUseFeature('edit_video')) {
        actions.push({ name: '编辑视频', ... });
    }

    if (this.canUseFeature('create_batch')) {
        actions.push({ name: '创建批次', ... }); // 员工看不到
    }

    if (this.canUseFeature('delete_video')) {
        actions.push({ name: '删除视频', ... }); // 员工看不到
    }

    return actions;
}
```

## 🎉 最终效果

### 员工登录后的体验：

1. **底部导航** - 可以正常访问"内容库"页面
2. **用户管理** - 可以正常管理和审核用户
3. **批次页面** - 可以查看批次列表，但没有创建按钮
4. **批次详情** - 可以查看批次详情，复制分享链接，查看数据
5. **视频详情** - 只能查看视频，没有"创建批次"和"删除视频"按钮

### 管理员登录后的体验：

1. **完整功能** - 可以访问所有页面和功能
2. **视频管理** - 可以编辑、删除视频，创建批次
3. **批次管理** - 可以查看和管理所有批次

## 🎯 权限分工明确

- **员工**：专注用户管理和审核，不接触视频和批次
- **管理员**：拥有完整的系统管理权限
- **超管**：拥有所有权限，包括删除等高危操作

这样的权限设计符合业务需求，确保了功能的合理分工和系统安全。
