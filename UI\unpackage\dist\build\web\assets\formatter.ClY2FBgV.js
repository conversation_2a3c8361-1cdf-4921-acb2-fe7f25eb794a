class t{static formatDuration(t,r=!1){if(!t||t<0)return r?"00:00:00":"00:00";const a=Math.floor(t/3600),e=Math.floor(t%3600/60),o=Math.floor(t%60);return r||a>0?`${a.toString().padStart(2,"0")}:${e.toString().padStart(2,"0")}:${o.toString().padStart(2,"0")}`:`${e.toString().padStart(2,"0")}:${o.toString().padStart(2,"0")}`}static formatDetailedDuration(t){if(!t||t<0)return"0秒";const r=Math.floor(t/3600),a=Math.floor(t%3600/60),e=Math.floor(t%60);return r>0?`${r}小时${a}分${e}秒`:a>0?`${a}分${e}秒`:`${e}秒`}static parseDurationToSeconds(t){if(!t)return 0;const r=t.split(":");if(2===r.length){return 60*(parseInt(r[0])||0)+(parseInt(r[1])||0)}return parseInt(t)||0}static formatDate(t){if(!t)return"";try{const r=new Date(t);if(isNaN(r.getTime()))return"";const a=r.getFullYear(),e=(r.getMonth()+1).toString().padStart(2,"0");return`${a}-${e}-${r.getDate().toString().padStart(2,"0")}`}catch(r){return console.error("日期格式化失败:",r),""}}static formatDateTime(t){if(!t)return"未设置";try{const r=new Date(t);if(isNaN(r.getTime()))return"未设置";const a=r.getFullYear(),e=(r.getMonth()+1).toString().padStart(2,"0"),o=r.getDate().toString().padStart(2,"0"),n=r.getHours().toString().padStart(2,"0");return`${a}-${e}-${o} ${n}:${r.getMinutes().toString().padStart(2,"0")}`}catch(r){return console.error("日期时间格式化失败:",r),"未设置"}}static formatDetailedDateTime(t){if(!t)return"";try{const r=new Date(t);if(isNaN(r.getTime()))return"";const a=r.getFullYear(),e=(r.getMonth()+1).toString().padStart(2,"0"),o=r.getDate().toString().padStart(2,"0"),n=r.getHours().toString().padStart(2,"0"),i=r.getMinutes().toString().padStart(2,"0");return`${a}-${e}-${o} ${n}:${i}:${r.getSeconds().toString().padStart(2,"0")}`}catch(r){return console.error("详细日期时间格式化失败:",r),""}}static formatFileSize(t){if(!t||0===t)return"0 B";const r=Math.floor(Math.log(t)/Math.log(1024));return parseFloat((t/Math.pow(1024,r)).toFixed(2))+" "+["B","KB","MB","GB","TB"][r]}static formatNumber(t){return null==t?"0":t.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")}static formatPercentage(t,r=2){return null==t?"0.00%":(100*t).toFixed(r)+"%"}static formatAmount(t,r=2,a=""){return null==t?a+"0.00":a+t.toFixed(r)}static formatMoney(t,r="¥"){return this.formatAmount(t,2,r)}}const r=t.formatDuration,a=t.formatDate,e=t.formatDateTime,o=t.formatFileSize;export{o as a,r as b,e as c,a as f};
