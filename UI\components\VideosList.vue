<template>
    <view class="container">
        <!-- 视频素材列表 -->
        <view class="media-list-container">
            <!-- 视频卡片列表项 -->
            <MediaCard v-for="(media, index) in filteredMedia" :key="index" :item="formatVideoItem(media)"
                @click="viewMediaDetail" />

            <!-- 空状态 -->
            <u-empty v-if="filteredMedia.length === 0" mode="list" :text="`暂无${getStatusLabel(currentStatus)}视频`"
                iconSize="120" textSize="16" marginTop="100">
                <u-button type="primary" text="上传视频" @click="showUploadModal" size="normal" shape="round"
                    v-permission:feature="'upload_video'"></u-button>
            </u-empty>
        </view>

        <!-- 悬浮按钮组 -->
        <FloatingActionButton text="上传" type="primary" :initialPosition="{ right: 20, bottom: 180 }"
            @click="showUploadModal" v-permission:feature="'upload_video'" />

    </view>
</template>

<script>

import FloatingActionButton from '@/components/FloatingActionButton.vue';
import MediaCard from '@/components/MediaCard.vue';
import { queryVideos } from "@/api/video.js";
import mediaCommonMixin from "@/mixins/media-common.js";

export default {
    mixins: [mediaCommonMixin],
    components: {
        FloatingActionButton,
        MediaCard
    },
    data () {
        return {
            mediaList: [],
            currentStatus: 'all'
        }
    },
    computed: {
        filteredMedia () {
            let result = this.mediaList;

            // 按状态筛选
            if (this.currentStatus !== 'all') {
                result = result.filter(media => media.status === this.currentStatus);
            }

            return result;
        }
    },
    created () {
        // 加载视频
        this.loadAllMedia();

        // 监听刷新事件
        uni.$on('refreshVideoList', () => {
            this.loadAllMedia();
        });
    },

    beforeDestroy () {
        // 移除事件监听
        uni.$off('refreshVideoList');
    },

    methods: {
        async loadAllMedia () {
            try {
                this.showLoading("加载中...");

                const response = await queryVideos({
                    page: 1,
                    pageSize: 1000
                });

                if (response.success && response.data) {
                    this.mediaList = response.data.items.map(video => ({
                        id: video.id,
                        title: video.title,
                        description: video.description,
                        thumbnail: this.buildCompleteFileUrl(video.coverUrl) || '/assets/images/video-cover.jpg',
                        duration: this.formatDuration(video.duration),
                        uploader: video.creatorName || video.createdBy || '管理员',
                        uploadTime: video.createTime,
                        status: video.status,
                        videoUrl: this.buildCompleteFileUrl(video.videoUrl),
                        fileSize: video.fileSize,
                        views: video.views || 0,
                        likes: video.likes || 0,
                        comments: video.comments || 0
                    }));
                } else {
                    console.error('获取视频列表失败:', response.message);
                    this.showError(response.message || "获取视频列表失败");
                    this.mediaList = [];
                }

                this.hideLoading();
            } catch (error) {
                console.error('加载视频列表失败:', error);
                this.hideLoading();
                this.showError("加载失败");
                this.mediaList = [];
            }
        },

        showUploadModal () {
            this.safeNavigateTo('/pages/admin/media/upload');
        },

        viewMediaDetail (item) {
            // item 是从 MediaCard 传来的格式化后的数据，需要获取原始数据
            const media = item.originalData || item;

            if (!media || !media.id) {
                this.showError('视频ID无效');
                return;
            }

            this.safeNavigateTo(`/pages/admin/media/detail?id=${media.id}`);
        },



        // 获取视频状态文本（兼容数字状态）
        getVideoStatusText (media) {
            const statusMap = {
                0: '已下架',
                1: '已上架',
                2: '处理失败',
                3: '压缩中'
            };
            return statusMap[media.status] || '未知状态';
        },

        // 获取视频状态类型（兼容数字状态）
        getVideoStatusType (media) {
            const typeMap = {
                0: 'error',    // 已下架
                1: 'success',  // 已上架
                2: 'error',    // 处理失败
                3: 'warning'   // 压缩中
            };
            return typeMap[media.status] || 'info';
        },

        // 获取状态标签文本
        getStatusLabel (status) {
            const labelMap = {
                'all': '',
                0: '已下架',
                1: '已上架',
                2: '处理失败',
                3: '压缩中'
            };
            return labelMap[status] || '';
        },

        // 格式化文件大小
        formatFileSize (bytes) {
            if (!bytes) return '';
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(1024));
            return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
        },

        // 格式化视频项数据为MediaCard组件需要的格式
        formatVideoItem (media) {
            const metaItems = [
                {
                    icon: '📅',
                    text: this.formatDate(media.uploadTime)
                },
                {
                    icon: '👤',
                    text: media.uploader
                }
            ];

            if (media.fileSize) {
                metaItems.push({
                    icon: '📁',
                    text: this.formatFileSize(media.fileSize)
                });
            }

            return {
                id: media.id,
                title: media.title,
                description: media.description,
                thumbnail: media.thumbnail,
                duration: media.duration,
                statusText: this.getVideoStatusText(media),
                statusType: this.getVideoStatusType(media),
                metaItems: metaItems,
                originalData: media
            };
        }
    }
}
</script>

<style lang="scss" scoped>
@import '@/styles/index.scss';

.container {
    padding: 0;
    background-color: #f7f7f7;
}

/* 媒体列表容器 */
.media-list-container {
    padding: 20rpx;
    padding-top: 0;
}

/* 样式现在由 MediaCard 组件提供 */

/* 响应式设计 */
@media (max-width: 600px) {
    .video-card-content {
        flex-direction: column;
        gap: 16rpx;
    }

    .video-thumbnail {
        width: 100%;
        height: 200rpx;
    }
}
</style>
