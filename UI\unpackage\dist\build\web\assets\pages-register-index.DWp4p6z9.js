import{_ as e,D as s,W as r,V as a,r as i,b as t,c as o,w as l,i as n,d as c,f as d,g as u,o as m,h as g,j as h,t as f,k as b,l as k}from"./index-EyT8-w9G.js";import{s as p}from"./video-user.BGOpWwcP.js";const _=e({data:()=>({registerForm:{nickname:"",mobile:""},isLoading:!1,errors:{nickname:"",mobile:""}}),computed:{canSubmit(){return this.registerForm.nickname.trim()&&!this.isLoading}},methods:{validateNickname(){const e=this.registerForm.nickname.trim();return e?e.length<2?(this.errors.nickname="昵称至少2个字符",!1):e.length>20?(this.errors.nickname="昵称不能超过20个字符",!1):(this.errors.nickname="",!0):(this.errors.nickname="昵称不能为空",!1)},validateMobile(){const e=this.registerForm.mobile.trim();return e&&!/^1[3-9]\d{9}$/.test(e)?(this.errors.mobile="请输入正确的手机号",!1):(this.errors.mobile="",!0)},clearError(e){this.errors[e]=""},validateForm(){const e=this.validateNickname(),s=this.validateMobile();return e&&s},async handleRegister(){if(this.validateForm()){if(this.canSubmit&&!this.isLoading){this.isLoading=!0;try{const{nickname:e,mobile:a}=this.registerForm,i=await p({nickname:e.trim(),mobile:a.trim()||void 0});i.success&&i.data?(s("userInfo",i.data.userInfo),s("token",i.data.token),this.showToastMessage("注册成功！","success"),await this.delay(1500),r({url:"/pages/index/index"})):this.showToastMessage(i.msg||"注册失败","error")}catch(e){console.error("Register error:",e),this.showToastMessage("注册失败，请重试","error")}finally{this.isLoading=!1}}}else this.showToastMessage("请检查输入信息","error")},goToLogin(){a({url:"/pages/user-login/index"})},showToastMessage(e,s="success"){this.$refs.uToast.show({message:e,type:s,duration:3e3})},delay:e=>new Promise((s=>setTimeout(s,e)))}},[["render",function(e,s,r,a,p,_){const v=k,F=n,w=i(t("u-input"),c),y=i(t("u-button"),d),L=i(t("u-toast"),u);return m(),o(F,{class:"register-container"},{default:l((()=>[g(F,{class:"register-content"},{default:l((()=>[g(F,{class:"header-section"},{default:l((()=>[g(v,{class:"app-title"},{default:l((()=>[h("用户注册")])),_:1}),g(v,{class:"app-subtitle"},{default:l((()=>[h("创建您的账号开始观看视频")])),_:1})])),_:1}),g(F,{class:"form-container"},{default:l((()=>[g(F,{class:"input-group"},{default:l((()=>[g(v,{class:"input-label"},{default:l((()=>[h("昵称 *")])),_:1}),g(w,{modelValue:p.registerForm.nickname,"onUpdate:modelValue":s[0]||(s[0]=e=>p.registerForm.nickname=e),placeholder:"请输入您的昵称",border:"surround",clearable:"",error:!!p.errors.nickname,onBlur:_.validateNickname,onInput:s[1]||(s[1]=e=>_.clearError("nickname")),class:"register-input"},null,8,["modelValue","error","onBlur"]),p.errors.nickname?(m(),o(v,{key:0,class:"error-message"},{default:l((()=>[h(f(p.errors.nickname),1)])),_:1})):b("",!0)])),_:1}),g(F,{class:"input-group"},{default:l((()=>[g(v,{class:"input-label"},{default:l((()=>[h("手机号")])),_:1}),g(w,{modelValue:p.registerForm.mobile,"onUpdate:modelValue":s[2]||(s[2]=e=>p.registerForm.mobile=e),placeholder:"请输入手机号（可选）",border:"surround",clearable:"",error:!!p.errors.mobile,onBlur:_.validateMobile,onInput:s[3]||(s[3]=e=>_.clearError("mobile")),class:"register-input"},null,8,["modelValue","error","onBlur"]),p.errors.mobile?(m(),o(v,{key:0,class:"error-message"},{default:l((()=>[h(f(p.errors.mobile),1)])),_:1})):b("",!0)])),_:1}),g(y,{type:"primary",text:p.isLoading?"注册中...":"注册",loading:p.isLoading,disabled:!_.canSubmit||p.isLoading,onClick:_.handleRegister,class:"register-btn"},null,8,["text","loading","disabled","onClick"]),g(F,{class:"login-link-container"},{default:l((()=>[g(v,{class:"login-link-text"},{default:l((()=>[h("已有账号？")])),_:1}),g(v,{class:"login-link",onClick:_.goToLogin},{default:l((()=>[h("立即登录")])),_:1},8,["onClick"])])),_:1})])),_:1}),g(F,{class:"footer"},{default:l((()=>[g(v,{class:"footer-text"},{default:l((()=>[h("© 2024 视频分享系统")])),_:1}),g(v,{class:"footer-version"},{default:l((()=>[h("Version 1.0.0")])),_:1})])),_:1})])),_:1}),g(L,{ref:"uToast"},null,512)])),_:1})}],["__scopeId","data-v-2528251b"]]);export{_ as default};
