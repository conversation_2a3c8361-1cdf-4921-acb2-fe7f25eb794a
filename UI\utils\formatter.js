/**
 * 统一格式化工具
 * 合并原 format.js 和 data-formatter.js 的功能，提供统一的格式化接口
 */

/**
 * 统一格式化工具类
 */
export class Formatter {
  /**
   * 格式化视频时长（秒转为 MM:SS 或 HH:MM:SS 格式）
   * @param {number} seconds - 秒数
   * @param {boolean} includeHours - 是否包含小时
   * @returns {string} 格式化后的时长字符串
   */
  static formatDuration(seconds, includeHours = false) {
    if (!seconds || seconds < 0) return includeHours ? '00:00:00' : '00:00'

    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)

    if (includeHours || hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }

    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  /**
   * 格式化详细时长（秒转为 X小时Y分Z秒 格式）
   * @param {number} seconds - 秒数
   * @returns {string} 格式化后的时长字符串
   */
  static formatDetailedDuration(seconds) {
    if (!seconds || seconds < 0) return '0秒'

    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const remainingSecs = Math.floor(seconds % 60)

    if (hours > 0) {
      return `${hours}小时${minutes}分${remainingSecs}秒`
    } else if (minutes > 0) {
      return `${minutes}分${remainingSecs}秒`
    } else {
      return `${remainingSecs}秒`
    }
  }

  /**
   * 将时长格式（如 "2:30"）转换为秒数
   * @param {string} durationStr - 时长字符串
   * @returns {number} 秒数
   */
  static parseDurationToSeconds(durationStr) {
    if (!durationStr) return 0

    const parts = durationStr.split(':')
    if (parts.length === 2) {
      const minutes = parseInt(parts[0]) || 0
      const seconds = parseInt(parts[1]) || 0
      return minutes * 60 + seconds
    }

    return parseInt(durationStr) || 0
  }

  /**
   * 格式化日期（YYYY-MM-DD）
   * @param {string|Date} dateInput - 日期字符串或Date对象
   * @returns {string} 格式化后的日期字符串
   */
  static formatDate(dateInput) {
    if (!dateInput) return ''

    try {
      const date = new Date(dateInput)
      if (isNaN(date.getTime())) return ''

      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')

      return `${year}-${month}-${day}`
    } catch (error) {
      console.error('日期格式化失败:', error)
      return ''
    }
  }

  /**
   * 格式化日期时间（YYYY-MM-DD HH:mm）
   * @param {string|Date} dateInput - 日期字符串或Date对象
   * @returns {string} 格式化后的日期时间字符串
   */
  static formatDateTime(dateInput) {
    if (!dateInput) return '未设置'

    try {
      const date = new Date(dateInput)
      if (isNaN(date.getTime())) return '未设置'

      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      const hours = date.getHours().toString().padStart(2, '0')
      const minutes = date.getMinutes().toString().padStart(2, '0')

      return `${year}-${month}-${day} ${hours}:${minutes}`
    } catch (error) {
      console.error('日期时间格式化失败:', error)
      return '未设置'
    }
  }

  /**
   * 格式化详细日期时间（YYYY-MM-DD HH:mm:ss）
   * @param {string|Date} dateInput - 日期字符串或Date对象
   * @returns {string} 格式化后的详细日期时间字符串
   */
  static formatDetailedDateTime(dateInput) {
    if (!dateInput) return ''

    try {
      const date = new Date(dateInput)
      if (isNaN(date.getTime())) return ''

      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      const hours = date.getHours().toString().padStart(2, '0')
      const minutes = date.getMinutes().toString().padStart(2, '0')
      const seconds = date.getSeconds().toString().padStart(2, '0')

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    } catch (error) {
      console.error('详细日期时间格式化失败:', error)
      return ''
    }
  }

  /**
   * 格式化文件大小
   * @param {number} bytes - 字节数
   * @returns {string} 格式化后的文件大小
   */
  static formatFileSize(bytes) {
    if (!bytes || bytes === 0) return '0 B'

    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * 格式化数字（添加千分位分隔符）
   * @param {number} num - 数字
   * @returns {string} 格式化后的数字字符串
   */
  static formatNumber(num) {
    if (num === null || num === undefined) return '0'
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  }

  /**
   * 格式化百分比
   * @param {number} value - 数值（0-1之间）
   * @param {number} decimals - 小数位数，默认2位
   * @returns {string} 格式化后的百分比字符串
   */
  static formatPercentage(value, decimals = 2) {
    if (value === null || value === undefined) return '0.00%'
    return (value * 100).toFixed(decimals) + '%'
  }

  /**
   * 格式化金额（元）
   * @param {number} amount - 金额
   * @param {number} decimals - 小数位数，默认2位
   * @param {string} currency - 货币符号，默认为空
   * @returns {string} 格式化后的金额字符串
   */
  static formatAmount(amount, decimals = 2, currency = '') {
    if (amount === null || amount === undefined) return currency + '0.00'
    return currency + amount.toFixed(decimals)
  }

  /**
   * 格式化金额（带货币符号）
   * @param {number} amount - 金额
   * @param {string} currency - 货币符号，默认¥
   * @returns {string} 格式化后的金额字符串
   */
  static formatMoney(amount, currency = '¥') {
    return this.formatAmount(amount, 2, currency)
  }
}

// 状态映射配置
export const STATUS_MAPS = {
  user: {
    1: 'active',
    0: 'disabled',
    2: 'dismissed'
  },
  employee: {
    1: 'active',
    0: 'disabled',
    2: 'dismissed'
  },
  video: {
    0: 'offline',
    1: 'online',
    2: 'failed',
    3: 'compressing'
  }
}

// 状态文本映射
export const STATUS_TEXTS = {
  active: '正常',
  disabled: '禁用',
  dismissed: '离职',
  online: '上线',
  offline: '下线',
  failed: '失败',
  compressing: '压缩中'
}

/**
 * 数据格式化器（扩展功能）
 */
export class DataFormatter extends Formatter {
  /**
   * 获取状态文本
   * @param {string} status - 状态值
   * @param {string} type - 状态类型
   * @returns {string} 状态文本
   */
  static getStatusText(status, type = 'user') {
    return STATUS_TEXTS[status] || '未知'
  }

  /**
   * 从统计数据中提取时间维度统计
   * @param {Object} statistics - 统计数据
   * @param {string} type - 统计类型
   * @returns {Object} 时间维度统计
   */
  static extractTimeStats(statistics, type) {
    if (!statistics?.[type]) {
      return {
        today: 0,
        yesterday: 0,
        thisWeek: 0,
        thisMonth: 0
      }
    }

    const stats = statistics[type]
    return {
      today: stats.today || 0,
      yesterday: stats.yesterday || 0,
      thisWeek: stats.thisWeek || 0,
      thisMonth: stats.thisMonth || 0
    }
  }

  /**
   * 批量格式化数据
   * @param {Array} list - 数据列表
   * @param {Function} formatter - 格式化函数
   * @param {...any} args - 格式化函数参数
   * @returns {Array} 格式化后的数据列表
   */
  static formatList(list, formatter, ...args) {
    if (!Array.isArray(list)) return []
    return list.map(item => formatter(item, ...args)).filter(Boolean)
  }
}

// 便捷导出函数（保持向后兼容）
export const formatDuration = Formatter.formatDuration
export const formatDetailedDuration = Formatter.formatDetailedDuration
export const formatDate = Formatter.formatDate
export const formatDateTime = Formatter.formatDateTime
export const formatDetailedDateTime = Formatter.formatDetailedDateTime
export const parseDurationToSeconds = Formatter.parseDurationToSeconds
export const formatFileSize = Formatter.formatFileSize
export const formatNumber = Formatter.formatNumber
export const formatPercentage = Formatter.formatPercentage
export const formatAmount = Formatter.formatAmount
export const formatMoney = Formatter.formatMoney
export const getStatusText = DataFormatter.getStatusText
export const extractTimeStats = DataFormatter.extractTimeStats
export const formatList = DataFormatter.formatList

// 默认导出
export default {
  Formatter,
  DataFormatter,
  STATUS_MAPS,
  STATUS_TEXTS,
  
  // 便捷函数
  formatDuration,
  formatDetailedDuration,
  formatDate,
  formatDateTime,
  formatDetailedDateTime,
  parseDurationToSeconds,
  formatFileSize,
  formatNumber,
  formatPercentage,
  formatAmount,
  formatMoney,
  getStatusText,
  extractTimeStats,
  formatList
}
