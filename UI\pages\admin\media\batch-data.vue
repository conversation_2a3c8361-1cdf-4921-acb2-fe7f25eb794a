<template>
    <view class="data-page">
        <!-- 数据展示组件 -->
        <BatchDataDisplay :batchId="batchId" :batchData="batchData" />
    </view>
</template>

<script>
import { getBatchDetail } from "@/api/batch.js";
import BatchDataDisplay from "@/components/BatchDataDisplay.vue";
import { showErrorAndGoBack, apiWrapper } from "@/utils/admin-helpers.js";

export default {
    name: 'BatchDataPage',
    components: {
        BatchDataDisplay
    },
    data () {
        return {
            batchId: '',
            batchData: {
                id: '',
                title: '',
                totalViews: 0,
                totalReward: 0,
                totalStudents: 0
            }
        }
    },
    async onLoad (options) {
        const { id } = options ?? {};

        if (id) {
            this.batchId = id;
            await this.loadBatchData();
        } else {
            showErrorAndGoBack('批次ID缺失');
        }
    },
    methods: {
        async loadBatchData () {
            const response = await apiWrapper(
                () => getBatchDetail(this.batchId),
                '加载批次数据...',
                '加载批次数据失败'
            );

            if (response.success && response.data) {
                const batch = response.data;
                this.batchData = {
                    id: batch.id,
                    title: batch.name ?? batch.title,
                    totalViews: batch.currentParticipants ?? 0,
                    totalReward: batch.rewardAmount ?? 0,
                    totalStudents: batch.totalStudents ?? batch.currentParticipants ?? 0,
                    redPacketAmount: batch.redPacketAmount ?? 0,
                    // 传递完整的统计数据
                    statistics: batch.statistics ?? null
                };


            } else {
                throw new Error(response.msg || '获取批次数据失败');
            }
        },
        goBack () {
            uni.navigateBack();
        }
    }
}
</script>

<style lang="scss">
@import '@/styles/index.scss';

.data-page {
    width: 100%;
    min-height: 100vh;
    background: #f8faff;
}
</style>
