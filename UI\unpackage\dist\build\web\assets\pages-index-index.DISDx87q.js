import{_ as t,o as a,c as e,w as s,j as l,t as n,n as i,l as u,m as o,p as r,a as c,s as d,q as f,u as m,v as w,x as _,y as p,i as h,h as v,z as C,k as g}from"./index-EyT8-w9G.js";import{T as V}from"./TimeFilter.77-OOFtV.js";import{g as b}from"./dashboard.DSQwUbgy.js";import{p as D}from"./permission-mixin.OCUMGXbm.js";function y(t={}){return o.get("/Statistics/overview",t)}const S=t({mixins:[D],components:{CountUp:t({name:"CountUp",props:{endValue:{type:[Number,String],required:!0,default:0},suffix:{type:String,default:""},duration:{type:Number,default:1e3},decimals:{type:Number,default:0},customClass:{type:String,default:""}},data:()=>({displayValue:"0",startValue:0,interval:null}),watch:{endValue:{handler(t){null!=t&&this.startAnimation(t)},immediate:!0}},methods:{startAnimation(t){this.interval&&clearInterval(this.interval);const a=this.parseValue(t),e=this.parseValue(this.displayValue);if(e===a)return void(this.displayValue=this.formatNumber(a));const s=this.duration,l=Math.min(s/20,25),n=(a-e)/20;let i=0;this.interval=setInterval((()=>{if(i++,i>=20)this.displayValue=this.formatNumber(a),clearInterval(this.interval);else{const t=e+n*i;this.displayValue=this.formatNumber(t)}}),l)},parseValue(t){const a=parseFloat(t);return isNaN(a)?0:a},formatNumber(t){const a=Number(t).toFixed(this.decimals);return isNaN(a)?"0":a}},beforeDestroy(){this.interval&&clearInterval(this.interval)}},[["render",function(t,o,r,c,d,f){const m=u;return a(),e(m,{class:i(r.customClass)},{default:s((()=>[l(n(d.displayValue)+n(r.suffix),1)])),_:1},8,["class"])}],["__scopeId","data-v-068eabf7"]]),TimeFilter:V},data:()=>({statisticsData:{totalUsers:1234,newUsersToday:56,viewerCount:789,completeRate:0,answerUserCount:0,correctRate:0,totalRewardAmount:0,untaggedUserCount:0,videoStats:{viewCount:2345,completeViewCount:1876,completeRate:79.98,avgViewDuration:0},quizStats:{answerCount:1567,correctAnswerCount:1234,correctRate:78.75},rewardStats:{rewardCount:45,rewardAmountYuan:1250.5}},activeTimeFilter:"today",customDateRange:null,sectionState:{courses:!0,quizzes:!0,rewards:!0}}),onLoad(){const t=window.location.href;if(console.log("首页onLoad - 当前URL:",t),t.includes("/pages/video/index")){console.log("检测到视频页面访问，直接跳转");const t=window.location.hash;if(t&&t.length>1){const a=t.substring(1);return console.log("跳转到视频页面:",a),void r({url:a})}}this.canAccessPage("/pages/index/index")?(console.log("当前用户类型:",this.currentUserType),this.initDashboardData(),d()):c.redirectToLogin()},onShow(){d()},methods:{async initDashboardData(){try{f({title:"加载数据中..."}),await this.loadStatisticsData("today"),m()}catch(t){console.error("初始化数据失败:",t),m(),w({title:"数据加载失败",icon:"none",duration:2e3})}},async loadStatisticsData(t,a=null,e=null){try{let l={};if(a&&e)l={startDate:a,endDate:e};else if("today"===t){const t=new Date,a=t=>`${t.getFullYear()}-${(t.getMonth()+1).toString().padStart(2,"0")}-${t.getDate().toString().padStart(2,"0")}`;l.startDate=a(t),l.endDate=a(t)}let n=null,i=null;try{[n,i]=await Promise.all([y(l),b(l)])}catch(s){console.error("API调用失败:",s)}const u=n&&n.success,o=i&&i.success;if(u&&o){const t=n.data,a=i.data;this.statisticsData={totalUsers:a.totalMembers||0,newUsersToday:a.todayNewMembers||0,viewerCount:a.viewerCount||0,completeRate:a.completeRate||0,answerUserCount:a.answerUserCount||0,correctRate:a.correctRate||0,totalRewardAmount:a.totalRewardAmount||0,untaggedUserCount:a.untaggedUserCount||0,videoStats:{viewCount:t.totalViewCount||0,completeViewCount:t.totalCompleteViewCount||0,completeRate:t.avgCompleteRate||0,avgViewDuration:0},quizStats:{answerCount:t.totalAnswerCount||0,correctAnswerCount:t.totalCorrectAnswerCount||0,correctRate:t.avgCorrectRate||0},rewardStats:{rewardCount:t.totalRewardCount||0,rewardAmountYuan:t.totalRewardAmountYuan||0}}}else console.warn("API数据获取失败，使用模拟数据:",{overviewSuccess:u,keyMetricsSuccess:o,overviewResponse:n?n.msg:"null",keyMetricsResponse:i?i.msg:"null"}),w({title:"数据加载失败",icon:"none",duration:2e3})}catch(l){throw console.error("加载统计数据失败:",l),l}},async selectTimeTab(t){try{f({title:"加载中..."}),await this.loadStatisticsData(t),m()}catch(a){console.error("切换时间标签失败:",a),m(),w({title:"数据加载失败",icon:"none",duration:2e3})}},async handleTimeFilterChange(t){try{this.activeTimeFilter=t,this.customDateRange={startDate:t.startDate,endDate:t.endDate},console.log("时间筛选变化:",t),f({title:"加载中..."}),await this.loadStatisticsData(t.type,t.startDate,t.endDate),m(),w({title:`已加载${t.startDate}至${t.endDate}的数据`,icon:"none",duration:2e3})}catch(a){console.error("时间筛选数据加载失败:",a),m(),w({title:"数据加载失败",icon:"none",duration:2e3})}}}},[["render",function(t,n,i,o,r,c){const d=_("CountUp"),f=h,m=u,w=_("TimeFilter"),V=p("permission");return a(),e(f,{class:"container"},{default:s((()=>[v(f,{class:"page-header"},{default:s((()=>[v(f,{class:"summary-section"},{default:s((()=>[v(f,{class:"stats-grid"},{default:s((()=>[v(f,{class:"stat-card"},{default:s((()=>[v(f,{class:"stat-value"},{default:s((()=>[v(d,{endValue:r.statisticsData.totalUsers,suffix:"人",duration:1e3,customClass:"stat-number"},null,8,["endValue"])])),_:1}),v(m,{class:"stat-label"},{default:s((()=>[l("会员总数")])),_:1})])),_:1}),v(f,{class:"stat-card"},{default:s((()=>[v(f,{class:"stat-value"},{default:s((()=>[v(d,{endValue:r.statisticsData.newUsersToday,suffix:"人",duration:1e3,customClass:"stat-number"},null,8,["endValue"])])),_:1}),v(m,{class:"stat-label"},{default:s((()=>[l("今日新增")])),_:1})])),_:1}),v(f,{class:"stat-card"},{default:s((()=>[v(f,{class:"stat-value"},{default:s((()=>[v(d,{endValue:r.statisticsData.viewerCount,suffix:"人",duration:1e3,customClass:"stat-number"},null,8,["endValue"])])),_:1}),v(m,{class:"stat-label"},{default:s((()=>[l("今日观看")])),_:1})])),_:1})])),_:1})])),_:1}),v(f,{class:"time-filter-section"},{default:s((()=>[v(f,{class:"time-filter-title"},{default:s((()=>[v(m,null,{default:s((()=>[l("时间筛选")])),_:1}),v(m,{class:"time-filter-subtitle"},{default:s((()=>[l("选择查看不同时间段的数据")])),_:1})])),_:1}),v(w,{modelValue:r.activeTimeFilter,"onUpdate:modelValue":n[0]||(n[0]=t=>r.activeTimeFilter=t),onChange:c.handleTimeFilterChange},null,8,["modelValue","onChange"])])),_:1})])),_:1}),v(f,{class:"page-content"},{default:s((()=>[C((a(),e(f,{class:"data-section"},{default:s((()=>[v(f,{class:"section-header"},{default:s((()=>[v(f,{class:"section-title-wrapper"},{default:s((()=>[v(m,{class:"section-title"},{default:s((()=>[l("课程统计")])),_:1})])),_:1})])),_:1}),r.sectionState.courses?(a(),e(f,{key:0,class:"section-content"},{default:s((()=>[v(f,{class:"stats-grid"},{default:s((()=>[v(f,{class:"stat-card"},{default:s((()=>[v(m,{class:"stat-label"},{default:s((()=>[l("观看次数")])),_:1}),v(f,{class:"stat-value"},{default:s((()=>[v(d,{endValue:r.statisticsData.videoStats.viewCount,suffix:"次",duration:1e3,customClass:"stat-number"},null,8,["endValue"])])),_:1})])),_:1}),v(f,{class:"stat-card"},{default:s((()=>[v(m,{class:"stat-label"},{default:s((()=>[l("完播次数")])),_:1}),v(f,{class:"stat-value"},{default:s((()=>[v(d,{endValue:r.statisticsData.videoStats.completeViewCount,suffix:"次",duration:1e3,customClass:"stat-number"},null,8,["endValue"])])),_:1})])),_:1}),v(f,{class:"stat-card"},{default:s((()=>[v(m,{class:"stat-label"},{default:s((()=>[l("完播率")])),_:1}),v(f,{class:"stat-value"},{default:s((()=>[v(d,{endValue:r.statisticsData.videoStats.completeRate,suffix:"%",duration:1e3,decimals:2,customClass:"stat-number"},null,8,["endValue"])])),_:1})])),_:1})])),_:1})])),_:1})):g("",!0)])),_:1})),[[V,"view_statistics","feature"]]),C((a(),e(f,{class:"data-section"},{default:s((()=>[v(f,{class:"section-header"},{default:s((()=>[v(f,{class:"section-title-wrapper"},{default:s((()=>[v(m,{class:"section-title"},{default:s((()=>[l("答题统计")])),_:1})])),_:1})])),_:1}),r.sectionState.quizzes?(a(),e(f,{key:0,class:"section-content"},{default:s((()=>[v(f,{class:"stats-grid"},{default:s((()=>[v(f,{class:"stat-card"},{default:s((()=>[v(m,{class:"stat-label"},{default:s((()=>[l("答题次数")])),_:1}),v(f,{class:"stat-value"},{default:s((()=>[v(d,{endValue:r.statisticsData.quizStats.answerCount,suffix:"次",duration:1e3,customClass:"stat-number"},null,8,["endValue"])])),_:1})])),_:1}),v(f,{class:"stat-card"},{default:s((()=>[v(m,{class:"stat-label"},{default:s((()=>[l("正确次数")])),_:1}),v(f,{class:"stat-value"},{default:s((()=>[v(d,{endValue:r.statisticsData.quizStats.correctAnswerCount,suffix:"次",duration:1e3,customClass:"stat-number"},null,8,["endValue"])])),_:1})])),_:1}),v(f,{class:"stat-card"},{default:s((()=>[v(m,{class:"stat-label"},{default:s((()=>[l("正确率")])),_:1}),v(f,{class:"stat-value"},{default:s((()=>[v(d,{endValue:r.statisticsData.quizStats.correctRate,suffix:"%",duration:1e3,decimals:2,customClass:"stat-number"},null,8,["endValue"])])),_:1})])),_:1})])),_:1})])),_:1})):g("",!0)])),_:1})),[[V,"view_statistics","feature"]]),C((a(),e(f,{class:"data-section"},{default:s((()=>[v(f,{class:"section-header"},{default:s((()=>[v(f,{class:"section-title-wrapper"},{default:s((()=>[v(m,{class:"section-title"},{default:s((()=>[l("红包统计")])),_:1})])),_:1})])),_:1}),r.sectionState.rewards?(a(),e(f,{key:0,class:"section-content"},{default:s((()=>[v(f,{class:"stats-grid grid-2"},{default:s((()=>[v(f,{class:"stat-card"},{default:s((()=>[v(m,{class:"stat-label"},{default:s((()=>[l("红包数量")])),_:1}),v(f,{class:"stat-value"},{default:s((()=>[v(d,{endValue:r.statisticsData.rewardStats.rewardCount,suffix:"个",duration:1e3,customClass:"stat-number"},null,8,["endValue"])])),_:1})])),_:1}),v(f,{class:"stat-card"},{default:s((()=>[v(m,{class:"stat-label"},{default:s((()=>[l("红包金额")])),_:1}),v(f,{class:"stat-value"},{default:s((()=>[v(d,{endValue:r.statisticsData.rewardStats.rewardAmountYuan,suffix:"元",duration:1e3,decimals:2,customClass:"stat-number"},null,8,["endValue"])])),_:1})])),_:1})])),_:1})])),_:1})):g("",!0)])),_:1})),[[V,"view_statistics","feature"]])])),_:1})])),_:1})}],["__scopeId","data-v-83da4a2a"]]);export{S as default};
