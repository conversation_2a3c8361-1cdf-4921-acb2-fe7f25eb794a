import{_ as e,x as t,o as a,c as s,w as o,h as i,A as l,B as n,F as r,ag as c,i as h,S as d,r as u,a4 as m,b as p,j as y,k as f,l as g,a2 as w,v as T,t as b,O as S,C as D,af as A,ai as _,aj as C,G as E,D as I,E as B,ak as V,al as v,am as x,q as L,u as P,V as k,N as M,y as U,e as F,f as H,z as R}from"./index-EyT8-w9G.js";import{T as $}from"./TimeFilter.77-OOFtV.js";import{U as N}from"./UserInfoCard.zzUNAAEA.js";import{p as O}from"./permission-mixin.OCUMGXbm.js";import{b as z,d as j,e as K,t as W}from"./sysuser.CdLraDd5.js";import{c as X}from"./employee.CTKHFWae.js";import{f as Z,c as G}from"./employee-data-mapper.B3YJtEOo.js";import"./media-common.B63tJHAk.js";import"./formatter.ClY2FBgV.js";const q=e({name:"UserList",components:{UserInfoCard:N},props:{items:{type:Array,default:()=>[]},timeFilter:{type:String,default:"today"},customDateRange:{type:Object,default:()=>({startDate:"",endDate:""})},listHeight:{type:Number,default:0},showEmployeesBtn:{type:Boolean,default:!0},showAccountBtn:{type:Boolean,default:!0}},methods:{handleItemClick(e){this.$emit("itemClick",e)},handleDisableAccount(e){this.$emit("disableAccount",e)},handleEnableAccount(e){this.$emit("enableAccount",e)}}},[["render",function(e,u,m,p,y,f){const g=t("UserInfoCard"),w=h,T=d;return a(),s(w,{class:"list-container",style:c({height:m.listHeight?m.listHeight+"px":"calc(100vh - 380rpx)"})},{default:o((()=>[i(T,{class:"scroll-list","scroll-y":""},{default:o((()=>[(a(!0),l(r,null,n(m.items,(e=>(a(),s(w,{class:"user-card",key:e.id,onClick:t=>f.handleItemClick(e)},{default:o((()=>[i(g,{userInfo:e,timeFilter:m.timeFilter,customDateRange:m.customDateRange,showDetailBtn:!1,showFooterBtns:!1,showEmployeesBtn:m.showEmployeesBtn,showAccountBtn:m.showAccountBtn,onDisableAccount:f.handleDisableAccount,onEnableAccount:f.handleEnableAccount},null,8,["userInfo","timeFilter","customDateRange","showEmployeesBtn","showAccountBtn","onDisableAccount","onEnableAccount"])])),_:2},1032,["onClick"])))),128)),i(w,{class:"list-bottom-space"})])),_:1})])),_:1},8,["style"])}],["__scopeId","data-v-1670b6e4"]]);const J=e({name:"SearchBox",props:{type:{type:String,default:"uview",validator:e=>["uview","native"].includes(e)},placeholder:{type:String,default:"请输入搜索关键词"},modelValue:{type:String,default:""}},emits:["update:modelValue","search","clear"],computed:{searchValue:{get(){return this.modelValue},set(e){this.$emit("update:modelValue",e)}}},methods:{handleSearch(){this.$emit("search",this.searchValue)},handleInput(){this.$emit("search",this.searchValue)},clearSearch(){this.searchValue="",this.$emit("clear"),this.$emit("search","")}}},[["render",function(e,t,l,n,r,c){const d=u(p("u-search"),m),T=g,b=w,S=h;return a(),s(S,{class:"search-container"},{default:o((()=>["uview"===l.type?(a(),s(d,{key:0,modelValue:c.searchValue,"onUpdate:modelValue":t[0]||(t[0]=e=>c.searchValue=e),placeholder:l.placeholder,onSearch:c.handleSearch,onCustom:c.handleSearch,shape:"round",showAction:!1,bgColor:"#f8f9fa",height:"35"},null,8,["modelValue","placeholder","onSearch","onCustom"])):(a(),s(S,{key:1,class:"search-box"},{default:o((()=>[i(S,{class:"search-input"},{default:o((()=>[i(T,{class:"search-icon"},{default:o((()=>[y("🔍")])),_:1}),i(b,{type:"text",modelValue:c.searchValue,"onUpdate:modelValue":t[1]||(t[1]=e=>c.searchValue=e),placeholder:l.placeholder,"confirm-type":"search",onConfirm:c.handleSearch,onInput:c.handleInput},null,8,["modelValue","placeholder","onConfirm","onInput"]),c.searchValue?(a(),s(T,{key:0,class:"clear-icon",onClick:c.clearSearch},{default:o((()=>[y("×")])),_:1},8,["onClick"])):f("",!0)])),_:1})])),_:1}))])),_:1})}],["__scopeId","data-v-348c6172"]]);const Y=e({name:"AddUserModal",props:{title:{type:String,default:"添加用户"},userType:{type:Number,required:!0},show:{type:Boolean,default:!1},loading:{type:Boolean,default:!1}},emits:["update:show","submit","close"],data(){return{formData:{userName:"",password:"",userType:this.userType,status:1}}},computed:{modalVisible:{get(){return this.show},set(e){this.$emit("update:show",e)}}},watch:{userType(e){this.formData.userType=e},show(e){e||this.resetForm()}},methods:{handleSubmit(){this.formData.userName&&this.formData.password?this.formData.password.length<6?T({title:"密码长度不能少于6位",icon:"none"}):this.$emit("submit",{...this.formData}):T({title:"用户名和密码不能为空",icon:"none"})},handleClose(){this.modalVisible=!1,this.$emit("close")},resetForm(){this.formData={userName:"",password:"",userType:this.userType,status:1}}}},[["render",function(e,t,l,n,r,c){const d=g,u=h,m=w,p=D;return c.modalVisible?(a(),s(u,{key:0,class:"modal-overlay",onClick:c.handleClose},{default:o((()=>[i(u,{class:"modal-container",onClick:t[2]||(t[2]=S((()=>{}),["stop"]))},{default:o((()=>[i(u,{class:"modal-header"},{default:o((()=>[i(d,{class:"modal-title"},{default:o((()=>[y(b(l.title),1)])),_:1})])),_:1}),i(u,{class:"modal-body"},{default:o((()=>[i(u,{class:"form-group"},{default:o((()=>[i(d,{class:"label"},{default:o((()=>[y("用户名")])),_:1}),i(m,{modelValue:r.formData.userName,"onUpdate:modelValue":t[0]||(t[0]=e=>r.formData.userName=e),placeholder:"请输入用户名",class:"input-field",type:"text"},null,8,["modelValue"])])),_:1}),i(u,{class:"form-group"},{default:o((()=>[i(d,{class:"label"},{default:o((()=>[y("密码")])),_:1}),i(m,{modelValue:r.formData.password,"onUpdate:modelValue":t[1]||(t[1]=e=>r.formData.password=e),placeholder:"请输入密码",class:"input-field",type:"password"},null,8,["modelValue"])])),_:1}),i(p,{class:"submit-btn",onClick:c.handleSubmit,disabled:l.loading},{default:o((()=>[y(b(l.loading?"创建中...":"确认添加"),1)])),_:1},8,["onClick","disabled"])])),_:1})])),_:1})])),_:1},8,["onClick"])):f("",!0)}],["__scopeId","data-v-c096f3fb"]]),Q={data:()=>({btnPosition:{left:20,top:300},startTouch:{x:0,y:0}}),methods:{initFloatingButtonPosition(){A({success:e=>{this.btnPosition={left:e.windowWidth-40-15,top:e.windowHeight-200}}})},moveFloatingBtn(e){const t=e.touches[0];this.btnPosition.left=t.clientX-20,this.btnPosition.top=t.clientY-20},touchStart(e){this.startTouch={x:e.touches[0].clientX,y:e.touches[0].clientY}},snapToEdge(){const e=_().windowWidth,t=_().windowHeight;this.btnPosition.left>e/2?this.btnPosition.left=e-40-15:this.btnPosition.left=15,this.btnPosition.top<100?this.btnPosition.top=100:this.btnPosition.top>t-150&&(this.btnPosition.top=t-150)}}},ee=new Map,te={DEFAULT_TTL:3e5,TTL:{VIDEO_LIST:18e4,VIDEO_DETAIL:6e5,BATCH_LIST:12e4,BATCH_DETAIL:3e5,STATISTICS:6e4,USER_PROGRESS:3e4},MAX_CACHE_SIZE:100};function ae(e,t){return"object"==typeof t&&(t=JSON.stringify(t)),`${e}:${t}`}class se{static set(e,t,a=te.DEFAULT_TTL){const s=Date.now()+a;if(ee.size>=te.MAX_CACHE_SIZE){const e=ee.keys().next().value;ee.delete(e)}ee.set(e,{value:t,expireTime:s})}static get(e){const t=ee.get(e);return t?Date.now()>t.expireTime?(ee.delete(e),null):t.value:null}static delete(e){ee.delete(e)}static clear(){ee.clear()}static getStats(){return{size:ee.size,maxSize:te.MAX_CACHE_SIZE,keys:Array.from(ee.keys())}}}class oe{static set(e,t,a=te.DEFAULT_TTL){try{const s={value:t,expireTime:Date.now()+a};I(`cache_${e}`,JSON.stringify(s))}catch(s){console.error("设置本地缓存失败:",s)}}static get(e){try{const t=B(`cache_${e}`);if(!t)return null;const a=JSON.parse(t);return Date.now()>a.expireTime?(E(`cache_${e}`),null):a.value}catch(t){return console.error("获取本地缓存失败:",t),null}}static delete(e){try{E(`cache_${e}`)}catch(t){console.error("删除本地缓存失败:",t)}}static clear(){try{const e=C();e.keys.filter((e=>e.startsWith("cache_"))).forEach((e=>{E(e)}))}catch(e){console.error("清空本地缓存失败:",e)}}}const ie={VIDEO_LIST:"VIDEO_LIST",VIDEO_DETAIL:"VIDEO_DETAIL",BATCH_LIST:"BATCH_LIST",BATCH_DETAIL:"BATCH_DETAIL",STATISTICS:"STATISTICS",USER_PROGRESS:"USER_PROGRESS"};const le=e({name:"UserManagement",mixins:[Q,{data:()=>({searchKeyword:"",searchDebouncer:null,currentPage:1,pageSize:20,totalCount:0,hasMore:!0,loading:!1,activeTimeFilter:"today",customDateRange:{startDate:"",endDate:""},listHeight:0,hasPageHeader:!1,selectedManagerId:null}),created(){this.searchDebouncer=function(e,t=500){let a=null;return function(...s){a&&clearTimeout(a),a=setTimeout((()=>{e.apply(this,s)}),t)}}(this.performSearch,500)},methods:{calculateListHeight(){const e=this;A({success(t){e.windowHeight=t.windowHeight,setTimeout((()=>{const a=V().in(e);a.select(".control-section").boundingClientRect(),a.exec((a=>{var s;if(a&&a[0]){const o=a[0].height||140,i=40,l=(null==(s=t.safeAreaInsets)?void 0:s.bottom)||0,n=e.hasPageHeader?70:0,r=10;e.listHeight=Math.max(e.windowHeight-o-i-l-n-r,550)}else e.listHeight=e.windowHeight-200}))}),500)}})},fixTabBarZIndex(){setTimeout((()=>{v({zIndex:9999})}),100)},handleTimeFilterChange(e){this.activeTimeFilter=e},handleCustomDateChange(e){this.customDateRange=e},onSearchInput(){this.searchDebouncer&&this.searchDebouncer()},async performSearch(){(class{static set(e,t,a,s={}){const{useMemory:o=!0,useStorage:i=!1,ttl:l=te.TTL[e]||te.DEFAULT_TTL}=s,n=ae(e,t);o&&se.set(n,a,l),i&&oe.set(n,a,l)}static get(e,t,a={}){const{useMemory:s=!0,useStorage:o=!1}=a,i=ae(e,t);if(s){const e=se.get(i);if(null!==e)return e}if(o){const t=oe.get(i);if(null!==t)return s&&se.set(i,t,te.TTL[e]||te.DEFAULT_TTL),t}return null}static delete(e,t){const a=ae(e,t);se.delete(a),oe.delete(a)}static clearType(e){const t=`${e}:`;Array.from(ee.keys()).forEach((e=>{e.startsWith(t)&&se.delete(e)}));try{C().keys.filter((e=>e.startsWith("cache_")&&e.substring(6).startsWith(t))).forEach((e=>{E(e)}))}catch(a){console.error("清空指定类型缓存失败:",a)}}}).clearType(ie.EMPLOYEE_LIST),this.loadData&&await this.loadData(!0)},async handleSearch(){await this.performSearch()},clearSearch(){this.searchKeyword=""},async loadMore(){this.hasMore&&!this.loading&&(this.currentPage++,this.loadData&&await this.loadData(!1))},async handlePullDownRefresh(){this.loadData&&await this.loadData(!0),x()},handleReachBottom(){this.loadMore()},handleShow(){this.fixTabBarZIndex(),this.calculateListHeight()}}},O],components:{TimeFilter:$,UserList:q,SearchBox:J,AddUserModal:Y},data:()=>({users:[],showModal:!1,selectedManagerId:null,pageType:"manager",userType:2,searchType:"uview",searchPlaceholder:"搜索用户姓名或手机号",modalType:"uview",modalTitle:"添加用户",buttonType:"uview",showEmployeesBtn:!1,showAccountBtn:!1}),computed:{filteredUsers(){if(!this.searchKeyword)return this.users;const e=this.searchKeyword.toLowerCase();return this.users.filter((t=>t.username.toLowerCase().includes(e)||t.phone&&t.phone.includes(e)))}},async onLoad(e){this.pageType=e.type||"manager",e.managerId&&(this.selectedManagerId=Number(e.managerId),this.pageType="employee"),this.configurePageByType(),await this.loadData(),this.calculateListHeight(),this.initFloatingButtonPosition()},onPullDownRefresh(){this.handlePullDownRefresh()},onReachBottom(){this.handleReachBottom()},onShow(){this.handleShow()},methods:{getCreatePermission(){return"manager"===this.pageType?"create_manager":"create_employee"},configurePageByType(){"manager"===this.pageType?(this.userType=2,this.searchPlaceholder="搜索管理姓名或手机号",this.modalTitle="添加管理",this.showEmployeesBtn=!1,this.showAccountBtn=!1):(this.userType=3,this.searchPlaceholder="搜索员工姓名或手机号",this.modalTitle="添加员工",this.showEmployeesBtn=!0,this.showAccountBtn=!0),this.searchType="uview",this.modalType="uview",this.buttonType="uview"},async loadData(e=!1){if(!this.loading)try{this.loading=!0,e&&(this.currentPage=1,this.users=[]),L({title:"加载中..."});const t={pageIndex:this.currentPage,pageSize:this.pageSize};let a;if(this.searchKeyword.trim()&&(t.userName=this.searchKeyword.trim(),t.realName=this.searchKeyword.trim()),a="manager"===this.pageType?await z(t):await j(t),!a.success||!a.data)throw new Error(a.msg||"获取用户列表失败");{const t=a.data.map((e=>Z(e)));this.totalCount=a.data.length,this.hasMore=a.data.length>=this.pageSize,this.users=e?t:[...this.users,...t]}P(),this.loading=!1}catch(t){P(),this.loading=!1,this.users=[],T({title:"加载失败",icon:"none"})}},showAddModal(){this.showModal=!0},closeModal(){this.showModal=!1},async handleAddUser(e){try{let t;if(L({title:"创建中..."}),"manager"===this.pageType)t=await K(e);else{const a={userName:e.userName,managerId:this.selectedManagerId||null},s=G(a,"employee");t=await X(s)}if(!t.success)throw new Error(t.msg||"创建用户失败");P(),T({title:"添加成功",icon:"success"}),await this.loadData(!0),this.closeModal()}catch(t){P(),T({title:t.message||"添加失败",icon:"none"})}},viewUserDetail(e){const t="manager"===this.pageType?"manager":"employee";k({url:`/pages/admin/users/member-list?id=${e.id}&type=${t}`})},async handleDisableAccount(e){"manager"!==this.pageType&&M({title:"确认禁用",content:`确定要禁用用户 ${e.username} 的账号吗？`,success:async t=>{if(t.confirm)try{L({title:"处理中..."});const t=await W(e.id,0);if(!t.success)throw new Error(t.msg||"禁用失败");e.disabled=!0,e.status=0,P(),T({title:"账号已禁用",icon:"success"})}catch(a){P(),e.disabled=!0,T({title:"账号已禁用",icon:"success"})}}})},async handleEnableAccount(e){"manager"!==this.pageType&&M({title:"确认启用",content:`确定要启用用户 ${e.username} 的账号吗？`,success:async t=>{if(t.confirm)try{L({title:"处理中..."});const t=await W(e.id,1);if(!t.success)throw new Error(t.msg||"启用失败");e.disabled=!1,e.status=1,P(),T({title:"账号已启用",icon:"success"})}catch(a){P(),e.disabled=!1,T({title:"账号已启用",icon:"success"})}}})}}},[["render",function(e,l,n,r,d,m){const y=t("TimeFilter"),f=t("SearchBox"),w=h,T=t("UserList"),b=u(p("u-icon"),F),D=u(p("u-button"),H),A=g,_=t("AddUserModal"),C=U("permission");return a(),s(w,{class:"user-management-container"},{default:o((()=>[i(w,{class:"control-section"},{default:o((()=>[i(y,{modelValue:e.activeTimeFilter,"onUpdate:modelValue":l[0]||(l[0]=t=>e.activeTimeFilter=t),onChange:e.handleTimeFilterChange,onCustomDateChange:e.handleCustomDateChange},null,8,["modelValue","onChange","onCustomDateChange"]),i(f,{type:d.searchType,placeholder:d.searchPlaceholder,modelValue:e.searchKeyword,"onUpdate:modelValue":l[1]||(l[1]=t=>e.searchKeyword=t),onSearch:e.handleSearch},null,8,["type","placeholder","modelValue","onSearch"])])),_:1}),i(w,{class:"container-list"},{default:o((()=>[i(T,{items:m.filteredUsers,timeFilter:e.activeTimeFilter,customDateRange:e.customDateRange,listHeight:e.listHeight,onItemClick:m.viewUserDetail,showEmployeesBtn:d.showEmployeesBtn,showAccountBtn:d.showAccountBtn,onDisableAccount:m.handleDisableAccount,onEnableAccount:m.handleEnableAccount},null,8,["items","timeFilter","customDateRange","listHeight","onItemClick","showEmployeesBtn","showAccountBtn","onDisableAccount","onEnableAccount"])])),_:1}),R((a(),s(w,{class:"floating-btn-wrapper",style:c({left:e.btnPosition.left+"px",top:e.btnPosition.top+"px"}),onTouchstart:e.touchStart,onTouchmove:S(e.moveFloatingBtn,["stop","prevent"]),onTouchend:e.snapToEdge},{default:o((()=>["uview"===d.buttonType?(a(),s(D,{key:0,type:"primary",shape:"circle",size:"large",onClick:m.showAddModal,customStyle:{width:"80rpx",height:"80rpx",fontSize:"24rpx",boxShadow:"0 6rpx 20rpx rgba(0, 122, 255, 0.4)"}},{default:o((()=>[i(b,{name:"plus",size:"20",color:"#fff"})])),_:1},8,["onClick","customStyle"])):(a(),s(w,{key:1,class:"floating-add-btn",onClick:m.showAddModal},{default:o((()=>[i(A,{class:"iconfont icon-add"})])),_:1},8,["onClick"]))])),_:1},8,["style","onTouchstart","onTouchmove","onTouchend"])),[[C,m.getCreatePermission(),"feature"]]),i(_,{type:d.modalType,title:d.modalTitle,userType:d.userType,show:d.showModal,"onUpdate:show":l[2]||(l[2]=e=>d.showModal=e),loading:e.loading,onSubmit:m.handleAddUser,onClose:m.closeModal},null,8,["type","title","userType","show","loading","onSubmit","onClose"])])),_:1})}],["__scopeId","data-v-ae8ca6f2"]]);export{le as default};
